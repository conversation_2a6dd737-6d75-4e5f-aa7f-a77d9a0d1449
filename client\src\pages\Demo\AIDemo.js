import React, { useState } from 'react';
import BillScanner from '../../components/AI/BillScanner';
import VoiceInput from '../../components/AI/VoiceInput';
import { Camera, Mic, Sparkles, Brain, Zap } from 'lucide-react';
import toast from 'react-hot-toast';

const AIDemo = () => {
  const [showBillScanner, setShowBillScanner] = useState(false);
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [extractedExpenses, setExtractedExpenses] = useState([]);

  const handleExpenseExtracted = (expenseData) => {
    setExtractedExpenses(prev => [expenseData, ...prev]);
    toast.success('AI extracted expense data successfully!');
  };

  const features = [
    {
      icon: Camera,
      title: 'Smart Bill Scanning',
      description: 'Upload or take a photo of any receipt or bill. Our AI will automatically extract the amount, date, merchant, and categorize the expense.',
      color: 'bg-blue-50 text-blue-600',
      examples: ['Restaurant receipts', 'Grocery bills', 'Gas station receipts', 'Online purchase confirmations']
    },
    {
      icon: Mic,
      title: 'Voice Expense Input',
      description: 'Simply speak your expense details naturally. Say things like "I spent 500 on milk yesterday" and watch the AI parse it perfectly.',
      color: 'bg-green-50 text-green-600',
      examples: ['"I spent 50 dollars on gas today"', '"Paid 120 for groceries yesterday"', '"Bought coffee for 5 bucks this morning"', '"Spent 200 on dinner last night"']
    },
    {
      icon: Brain,
      title: 'Smart Categorization',
      description: 'AI automatically categorizes your expenses based on context. Milk becomes "Food", gas becomes "Transportation", etc.',
      color: 'bg-purple-50 text-purple-600',
      examples: ['Food & Dining', 'Transportation', 'Shopping', 'Entertainment']
    },
    {
      icon: Zap,
      title: 'Instant Processing',
      description: 'Get results in seconds with high confidence scores. Review and edit before saving to ensure accuracy.',
      color: 'bg-orange-50 text-orange-600',
      examples: ['Real-time analysis', 'Confidence scoring', 'Editable results', 'One-click save']
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-primary-600 mr-2" />
            <h1 className="text-4xl font-bold text-gray-900">AI-Powered Expense Tracking</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the future of expense management with Gemini AI integration. 
            Scan bills, speak expenses, and let AI do the heavy lifting.
          </p>
        </div>

        {/* Demo Buttons */}
        <div className="flex justify-center space-x-6 mb-16">
          <button
            onClick={() => setShowBillScanner(true)}
            className="flex items-center px-8 py-4 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors shadow-lg"
          >
            <Camera className="h-6 w-6 mr-3" />
            Try Bill Scanner
          </button>
          <button
            onClick={() => setShowVoiceInput(true)}
            className="flex items-center px-8 py-4 bg-success-600 text-white rounded-xl hover:bg-success-700 transition-colors shadow-lg"
          >
            <Mic className="h-6 w-6 mr-3" />
            Try Voice Input
          </button>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 shadow-soft hover:shadow-medium transition-shadow">
              <div className={`inline-flex p-3 rounded-lg ${feature.color} mb-4`}>
                <feature.icon className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 mb-4">{feature.description}</p>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">Examples:</p>
                <ul className="space-y-1">
                  {feature.examples.map((example, idx) => (
                    <li key={idx} className="text-sm text-gray-500 flex items-center">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                      {example}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Extracted Expenses */}
        {extractedExpenses.length > 0 && (
          <div className="bg-white rounded-2xl p-8 shadow-soft">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">AI Extracted Expenses</h3>
            <div className="space-y-4">
              {extractedExpenses.map((expense, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{expense.title}</h4>
                    <span className="text-lg font-bold text-primary-600">
                      ${expense.amount}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Category:</span> {expense.category}
                    </div>
                    <div>
                      <span className="font-medium">Date:</span> {expense.date}
                    </div>
                    <div>
                      <span className="font-medium">Type:</span> {expense.type}
                    </div>
                    <div>
                      <span className="font-medium">Method:</span> {expense.paymentMethod || 'N/A'}
                    </div>
                  </div>
                  {expense.description && (
                    <p className="mt-2 text-sm text-gray-600">{expense.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* How it Works */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Input</h3>
              <p className="text-gray-600">Upload a bill photo or speak your expense details naturally</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-success-600">2</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Analysis</h3>
              <p className="text-gray-600">Gemini AI extracts amount, date, category, and other details</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">3</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Review & Save</h3>
              <p className="text-gray-600">Review the extracted data, make edits if needed, and save</p>
            </div>
          </div>
        </div>
      </div>

      {/* AI Modals */}
      {showBillScanner && (
        <BillScanner
          onExpenseExtracted={handleExpenseExtracted}
          onClose={() => setShowBillScanner(false)}
        />
      )}

      {showVoiceInput && (
        <VoiceInput
          onExpenseExtracted={handleExpenseExtracted}
          onClose={() => setShowVoiceInput(false)}
        />
      )}
    </div>
  );
};

export default AIDemo;
