# Splitify - Expense Tracking & Splitting Application

A comprehensive MERN stack application for tracking personal expenses and splitting costs with groups in real-time.

## Features

### Personal Expense Tracking
- Add, edit, and delete income and expenditures
- Interactive dashboard with charts and graphs
- Bar charts, pie charts, and trend analysis
- Category-wise expense breakdown

### Group Expense Splitting
- Create groups for shared expenses
- Real-time collaboration with multiple users
- Automatic calculation of who owes what
- Track payments and settlements

## Tech Stack

- **Frontend**: React.js with modern UI components
- **Backend**: Node.js with Express.js
- **Database**: MongoDB
- **Real-time**: Socket.io
- **Charts**: Chart.js / React-Chartjs-2
- **Styling**: CSS3 with modern design

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies for both client and server:
   ```bash
   npm run install-all
   ```

3. Set up environment variables (see .env.example)

4. Start the development servers:
   ```bash
   npm run dev
   ```

This will start both the React client (port 3000) and Express server (port 5000).

## Project Structure

```
splitify/
├── client/          # React frontend
├── server/          # Express backend
├── package.json     # Root package.json
└── README.md
```

## API Endpoints

### Authentication
- POST /api/auth/register
- POST /api/auth/login

### Expenses
- GET /api/expenses
- POST /api/expenses
- PUT /api/expenses/:id
- DELETE /api/expenses/:id

### Groups
- GET /api/groups
- POST /api/groups
- PUT /api/groups/:id
- DELETE /api/groups/:id

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request
