# 🤖 Splitify - AI-Powered Expense Tracking & Splitting

A comprehensive MERN stack application with **Gemini AI integration** for intelligent expense tracking, bill scanning, and voice input processing.

## 🌟 Key Features

### 🤖 AI-Powered Features (NEW!)
- **📸 Smart Bill Scanner**: Upload receipt photos, AI extracts amount, date, merchant, category
- **🎤 Voice Expense Input**: Speak naturally - "I spent 500 on milk yesterday"
- **🧠 Smart Categorization**: AI automatically categorizes expenses based on context
- **⚡ Real-time Processing**: Instant analysis with confidence scoring

### 💰 Personal Expense Tracking
- Add, edit, and delete income and expenditures
- Interactive dashboard with charts and graphs
- Bar charts, pie charts, and trend analysis
- Category-wise expense breakdown with AI suggestions

### 👥 Group Expense Splitting
- Create groups for shared expenses
- Real-time collaboration with multiple users
- Automatic calculation of who owes what
- Track payments and settlements

### 📊 Advanced Analytics
- Monthly/yearly expense trends
- Category-wise spending analysis
- Income vs expense comparisons
- Smart insights and recommendations

## 🛠️ Tech Stack

- **Frontend**: React.js with Tailwind CSS
- **Backend**: Node.js with Express.js
- **Database**: MongoDB with Mongoose
- **AI Integration**: Google Gemini API
- **Real-time**: Socket.io
- **Charts**: Chart.js / React-Chartjs-2
- **Authentication**: JWT with bcrypt

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v14 or higher) - [Download](https://nodejs.org/)
- **MongoDB** - Local or [MongoDB Atlas](https://www.mongodb.com/atlas)
- **Gemini API Key** - [Get from Google AI Studio](https://makersuite.google.com/)

### 🎯 One-Click Setup (Windows)
```bash
# Double-click to run:
setup-and-run.bat
```

### 📋 Manual Installation

1. **Install Dependencies**
   ```bash
   npm install
   cd server && npm install
   cd ../client && npm install
   ```

2. **Environment Setup**
   ```bash
   cd server
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start MongoDB**
   ```bash
   mongod --dbpath "C:\data\db"
   ```

4. **Seed Sample Data**
   ```bash
   cd server && npm run seed
   ```

5. **Start Application**
   ```bash
   npm run dev
   ```

### 🌐 Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Demo Login**: <EMAIL> / password123

## 🎯 AI Features Demo

### 📸 Smart Bill Scanner
```javascript
// Upload any receipt/bill image
POST /api/ai/analyze-bill
// AI Response:
{
  "title": "Grocery Store Receipt",
  "amount": 85.50,
  "date": "2024-01-15",
  "category": "food",
  "merchant": "Walmart",
  "confidence": 95
}
```

### 🎤 Voice Input Examples
```javascript
// Natural language processing
"I spent 500 on milk yesterday"
→ { amount: 500, category: "food", date: "2024-01-14" }

"Paid 50 dollars for gas today"
→ { amount: 50, category: "transportation", date: "2024-01-15" }

"Bought groceries for 120 rupees"
→ { amount: 120, category: "food", date: "2024-01-15" }
```

## 📁 Project Structure

```
splitify/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/AI/  # AI feature components
│   │   ├── pages/         # Main application pages
│   │   └── utils/api.js   # API integration
├── server/                # Express backend
│   ├── models/           # MongoDB schemas
│   ├── routes/           # API endpoints
│   ├── services/         # Gemini AI service
│   └── middleware/       # Authentication & validation
├── setup-and-run.bat    # One-click setup script
└── README.md
```

## API Endpoints

### Authentication
- POST /api/auth/register
- POST /api/auth/login

### Expenses
- GET /api/expenses
- POST /api/expenses
- PUT /api/expenses/:id
- DELETE /api/expenses/:id

### Groups
- GET /api/groups
- POST /api/groups
- PUT /api/groups/:id
- DELETE /api/groups/:id

### AI Features (NEW!)
- POST /api/ai/analyze-bill - Smart bill scanning
- POST /api/ai/process-voice - Voice expense input
- POST /api/ai/categorize - Smart categorization
- POST /api/ai/smart-suggestions - AI recommendations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request
