import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { groupsAPI } from '../../utils/api';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import GroupExpenseForm from './GroupExpenseForm';
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  DollarSign, 
  Receipt,
  UserPlus,
  Settings,
  TrendingDown,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

const GroupDetail = () => {
  const { id } = useParams();
  const [group, setGroup] = useState(null);
  const [expenses, setExpenses] = useState([]);
  const [balances, setBalances] = useState({});
  const [settlements, setSettlements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [activeTab, setActiveTab] = useState('expenses');

  useEffect(() => {
    if (id) {
      loadGroupData();
    }
  }, [id]);

  const loadGroupData = async () => {
    try {
      setLoading(true);
      const [groupResponse, expensesResponse, balancesResponse] = await Promise.all([
        groupsAPI.getGroups(),
        groupsAPI.getGroupExpenses(id),
        groupsAPI.getBalances(id)
      ]);

      const currentGroup = groupResponse.data.groups.find(g => g._id === id);
      setGroup(currentGroup);
      setExpenses(expensesResponse.data.expenses);
      setBalances(balancesResponse.data.balances);
      setSettlements(balancesResponse.data.settlements);
    } catch (error) {
      toast.error('Failed to load group data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddExpense = async (expenseData) => {
    try {
      await groupsAPI.addGroupExpense(id, expenseData);
      toast.success('Expense added successfully');
      setShowExpenseForm(false);
      loadGroupData();
    } catch (error) {
      toast.error('Failed to add expense');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900">Group not found</h2>
        <Link to="/groups" className="text-primary-600 hover:text-primary-500 mt-2 inline-block">
          ← Back to Groups
        </Link>
      </div>
    );
  }

  const tabs = [
    { id: 'expenses', name: 'Expenses', icon: Receipt },
    { id: 'balances', name: 'Balances', icon: DollarSign },
    { id: 'members', name: 'Members', icon: Users },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/groups"
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
            <p className="text-gray-500">{group.members.length} members • {group.currency}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="btn-secondary">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Member
          </button>
          <button
            onClick={() => setShowExpenseForm(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Expense
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="stat-card">
          <div className="flex items-center">
            <div className="p-3 bg-primary-50 rounded-lg">
              <DollarSign className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Expenses</p>
              <p className="text-2xl font-semibold text-gray-900">
                ${group.totalExpenses?.toLocaleString() || '0'}
              </p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="p-3 bg-success-50 rounded-lg">
              <Receipt className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Transactions</p>
              <p className="text-2xl font-semibold text-gray-900">{expenses.length}</p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="p-3 bg-orange-50 rounded-lg">
              <Users className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Members</p>
              <p className="text-2xl font-semibold text-gray-900">{group.members.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'expenses' && (
          <ExpensesTab expenses={expenses} />
        )}
        
        {activeTab === 'balances' && (
          <BalancesTab balances={balances} settlements={settlements} group={group} />
        )}
        
        {activeTab === 'members' && (
          <MembersTab group={group} />
        )}
      </div>

      {/* Group Expense Form Modal */}
      {showExpenseForm && (
        <GroupExpenseForm
          group={group}
          onSubmit={handleAddExpense}
          onClose={() => setShowExpenseForm(false)}
        />
      )}
    </div>
  );
};

// Expenses Tab Component
const ExpensesTab = ({ expenses }) => {
  if (expenses.length === 0) {
    return (
      <div className="card">
        <div className="px-6 py-12 text-center">
          <Receipt className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No expenses yet</h3>
          <p className="text-gray-500">Add your first group expense to get started.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {expenses.map((expense) => (
        <div key={expense._id} className="card p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-danger-50 rounded-lg">
                <TrendingDown className="h-5 w-5 text-danger-600" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {expense.title}
                  </h3>
                  <span className="text-xl font-bold text-danger-600">
                    ${expense.amount.toLocaleString()}
                  </span>
                </div>
                
                <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                  <span>Paid by {expense.paidBy.name}</span>
                  <span>•</span>
                  <span>{expense.category.charAt(0).toUpperCase() + expense.category.slice(1)}</span>
                  <span>•</span>
                  <span>{format(new Date(expense.date), 'MMM dd, yyyy')}</span>
                </div>
                
                {expense.description && (
                  <p className="mt-2 text-sm text-gray-600">{expense.description}</p>
                )}
                
                <div className="mt-2 text-sm text-gray-500">
                  Split among {expense.splits.length} member{expense.splits.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Balances Tab Component
const BalancesTab = ({ balances, settlements, group }) => {
  return (
    <div className="space-y-6">
      {/* Member Balances */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Member Balances</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {group.members.map((member) => {
            const balance = balances[member.user._id] || 0;
            return (
              <div key={member.user._id} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {member.user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">{member.user.name}</p>
                    <p className="text-sm text-gray-500">{member.user.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${
                    balance > 0 ? 'text-success-600' : balance < 0 ? 'text-danger-600' : 'text-gray-900'
                  }`}>
                    {balance > 0 ? '+' : ''}${balance.toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {balance > 0 ? 'is owed' : balance < 0 ? 'owes' : 'settled up'}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Settlement Suggestions */}
      {settlements.length > 0 && (
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Suggested Settlements</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {settlements.map((settlement, index) => (
              <div key={index} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="h-8 w-8 rounded-full bg-danger-100 flex items-center justify-center">
                      <span className="text-xs font-medium text-danger-800">
                        {settlement.fromUser.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">pays</span>
                    <div className="h-8 w-8 rounded-full bg-success-100 flex items-center justify-center">
                      <span className="text-xs font-medium text-success-800">
                        {settlement.toUser.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {settlement.fromUser.name} → {settlement.toUser.name}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${settlement.amount.toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Members Tab Component
const MembersTab = ({ group }) => {
  return (
    <div className="card">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Group Members</h3>
      </div>
      <div className="divide-y divide-gray-200">
        {group.members.map((member) => (
          <div key={member.user._id} className="px-6 py-4 flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {member.user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-900">{member.user.name}</p>
                <p className="text-sm text-gray-500">{member.user.email}</p>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                member.role === 'admin' 
                  ? 'bg-primary-100 text-primary-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {member.role}
              </span>
              <p className="text-xs text-gray-500 mt-1">
                Joined {format(new Date(member.joinedAt), 'MMM dd, yyyy')}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GroupDetail;
