#!/usr/bin/env python3
"""
Splitify Demo Server - Python Flask Backend
Demonstrates the AI-powered expense tracking functionality
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import json
import os
import base64
from datetime import datetime, timedelta
import random

app = Flask(__name__)
CORS(app)

# Sample data
users = {
    "<EMAIL>": {
        "id": "1",
        "name": "Demo User",
        "email": "<EMAIL>",
        "password": "password123",
        "totalIncome": 5000,
        "totalExpenses": 3200
    }
}

expenses = [
    {
        "id": "1",
        "title": "Grocery Shopping",
        "amount": 85.50,
        "category": "food",
        "date": "2024-01-15",
        "type": "expense",
        "description": "Weekly groceries from Walmart"
    },
    {
        "id": "2", 
        "title": "Gas Station",
        "amount": 45.00,
        "category": "transportation",
        "date": "2024-01-14",
        "type": "expense",
        "description": "Fuel for car"
    },
    {
        "id": "3",
        "title": "Freelance Payment",
        "amount": 1200.00,
        "category": "freelance",
        "date": "2024-01-13",
        "type": "income",
        "description": "Web development project"
    }
]

groups = [
    {
        "id": "1",
        "name": "Weekend Trip",
        "description": "Expenses for our weekend getaway",
        "totalExpenses": 450.00,
        "members": ["Demo User", "John Doe", "Jane Smith"]
    }
]

# Simulate Gemini AI responses
def simulate_bill_analysis(image_data=None):
    """Simulate AI bill analysis"""
    sample_results = [
        {
            "title": "Grocery Store Receipt",
            "amount": 85.50,
            "date": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
            "category": "food",
            "merchant": "Walmart Supercenter",
            "items": ["Milk", "Bread", "Eggs", "Apples"],
            "paymentMethod": "credit-card",
            "confidence": 95
        },
        {
            "title": "Gas Station",
            "amount": 45.00,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "category": "transportation", 
            "merchant": "Shell Gas Station",
            "items": ["Regular Gasoline"],
            "paymentMethod": "debit-card",
            "confidence": 92
        },
        {
            "title": "Restaurant Bill",
            "amount": 67.80,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "category": "food",
            "merchant": "Pizza Palace",
            "items": ["Large Pizza", "Soft Drinks", "Garlic Bread"],
            "paymentMethod": "credit-card",
            "confidence": 88
        }
    ]
    return random.choice(sample_results)

def simulate_voice_processing(voice_text):
    """Simulate AI voice processing"""
    voice_text = voice_text.lower()
    
    # Extract amount
    amount = 0
    if "500" in voice_text or "five hundred" in voice_text:
        amount = 500
    elif "50" in voice_text or "fifty" in voice_text:
        amount = 50
    elif "120" in voice_text:
        amount = 120
    elif "25" in voice_text or "twenty five" in voice_text:
        amount = 25
    
    # Extract category
    category = "other-expense"
    if "milk" in voice_text or "grocery" in voice_text or "food" in voice_text:
        category = "food"
    elif "gas" in voice_text or "fuel" in voice_text:
        category = "transportation"
    elif "coffee" in voice_text or "restaurant" in voice_text:
        category = "food"
    
    # Extract date
    date = datetime.now().strftime("%Y-%m-%d")
    if "yesterday" in voice_text:
        date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    elif "today" in voice_text:
        date = datetime.now().strftime("%Y-%m-%d")
    
    # Generate title
    title = "Expense"
    if "milk" in voice_text:
        title = "Milk purchase"
    elif "gas" in voice_text:
        title = "Gas station"
    elif "coffee" in voice_text:
        title = "Coffee shop"
    elif "grocery" in voice_text:
        title = "Grocery shopping"
    
    return {
        "title": title,
        "amount": amount,
        "date": date,
        "category": category,
        "description": f"Parsed from voice: {voice_text}",
        "confidence": random.randint(85, 95)
    }

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.json
    email = data.get('email')
    password = data.get('password')
    
    if email in users and users[email]['password'] == password:
        return jsonify({
            "success": True,
            "token": "demo-jwt-token",
            "user": {
                "id": users[email]['id'],
                "name": users[email]['name'],
                "email": users[email]['email'],
                "totalIncome": users[email]['totalIncome'],
                "totalExpenses": users[email]['totalExpenses']
            }
        })
    else:
        return jsonify({"success": False, "message": "Invalid credentials"}), 401

@app.route('/api/expenses', methods=['GET'])
def get_expenses():
    return jsonify({"expenses": expenses})

@app.route('/api/expenses', methods=['POST'])
def create_expense():
    data = request.json
    new_expense = {
        "id": str(len(expenses) + 1),
        "title": data.get('title'),
        "amount": data.get('amount'),
        "category": data.get('category'),
        "date": data.get('date', datetime.now().strftime("%Y-%m-%d")),
        "type": data.get('type', 'expense'),
        "description": data.get('description', '')
    }
    expenses.append(new_expense)
    return jsonify({"success": True, "expense": new_expense})

@app.route('/api/ai/analyze-bill', methods=['POST'])
def analyze_bill():
    # Simulate processing time
    import time
    time.sleep(1)
    
    result = simulate_bill_analysis()
    return jsonify({
        "success": True,
        "message": "Bill analyzed successfully",
        "data": result
    })

@app.route('/api/ai/process-voice', methods=['POST'])
def process_voice():
    data = request.json
    voice_text = data.get('voiceText', '')
    
    # Simulate processing time
    import time
    time.sleep(1)
    
    result = simulate_voice_processing(voice_text)
    return jsonify({
        "success": True,
        "message": "Voice input processed successfully",
        "analysisData": result
    })

@app.route('/api/dashboard', methods=['GET'])
def get_dashboard():
    # Calculate category breakdown
    category_breakdown = {}
    for expense in expenses:
        if expense['type'] == 'expense':
            category = expense['category']
            category_breakdown[category] = category_breakdown.get(category, 0) + expense['amount']
    
    category_list = [{"_id": k, "total": v} for k, v in category_breakdown.items()]
    
    total_income = sum(e['amount'] for e in expenses if e['type'] == 'income')
    total_expenses = sum(e['amount'] for e in expenses if e['type'] == 'expense')
    
    return jsonify({
        "user": users["<EMAIL>"],
        "recentExpenses": expenses[-5:],
        "monthlyStats": {
            "income": total_income,
            "expenses": total_expenses,
            "balance": total_income - total_expenses
        },
        "categoryBreakdown": category_list,
        "groups": groups
    })

@app.route('/api/groups', methods=['GET'])
def get_groups():
    return jsonify({"groups": groups})

if __name__ == '__main__':
    print("🚀 Starting Splitify Demo Server...")
    print("=" * 50)
    print("🌐 Frontend: http://localhost:5000")
    print("🔧 Backend API: http://localhost:5000/api")
    print("🤖 AI Features: Simulated (Gemini API)")
    print("=" * 50)
    print("📧 Demo Login: <EMAIL>")
    print("🔑 Password: password123")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
