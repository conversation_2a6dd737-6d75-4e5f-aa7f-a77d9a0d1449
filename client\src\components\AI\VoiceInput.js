import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Mic<PERSON><PERSON>, X, Volume2, CheckCircle, AlertCircle } from 'lucide-react';
import { aiAPI } from '../../utils/api';
import LoadingSpinner from '../UI/LoadingSpinner';
import toast from 'react-hot-toast';

const VoiceInput = ({ onExpenseExtracted, onClose }) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [processing, setProcessing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [editableData, setEditableData] = useState(null);
  const [isSupported, setIsSupported] = useState(false);
  const recognitionRef = useRef(null);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      setIsSupported(true);
      recognitionRef.current = new SpeechRecognition();
      
      const recognition = recognitionRef.current;
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        setTranscript(finalTranscript + interimTranscript);
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        toast.error('Speech recognition error. Please try again.');
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setTranscript('');
      setAnalysisResult(null);
      setEditableData(null);
      recognitionRef.current.start();
      setIsListening(true);
      toast.success('Listening... Speak your expense details');
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };

  const processVoiceInput = async () => {
    if (!transcript.trim()) {
      toast.error('No speech detected. Please try again.');
      return;
    }

    setProcessing(true);
    try {
      const response = await aiAPI.processVoice(transcript.trim());
      const result = response.data.analysisData;
      
      setAnalysisResult(result);
      setEditableData({
        title: result.title,
        amount: result.amount,
        date: result.date,
        category: result.category,
        description: result.description || '',
      });
      
      toast.success(`Voice input processed with ${result.confidence}% confidence`);
    } catch (error) {
      toast.error('Failed to process voice input. Please try again.');
      console.error('Voice processing error:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleDataChange = (field, value) => {
    setEditableData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const createExpense = async () => {
    if (!editableData) return;

    try {
      const expenseData = {
        ...editableData,
        amount: parseFloat(editableData.amount),
        type: 'expense',
        paymentMethod: 'cash',
        tags: [],
      };

      onExpenseExtracted(expenseData);
      toast.success('Expense created from voice input!');
      onClose();
    } catch (error) {
      toast.error('Failed to create expense');
      console.error('Expense creation error:', error);
    }
  };

  const categories = [
    { value: 'food', label: 'Food' },
    { value: 'transportation', label: 'Transportation' },
    { value: 'housing', label: 'Housing' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'shopping', label: 'Shopping' },
    { value: 'education', label: 'Education' },
    { value: 'travel', label: 'Travel' },
    { value: 'other-expense', label: 'Other' },
  ];

  if (!isSupported) {
    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
              <div className="text-center">
                <MicOff className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Speech Recognition Not Supported
                </h3>
                <p className="text-gray-500 mb-4">
                  Your browser doesn't support speech recognition. Please use a modern browser like Chrome or Edge.
                </p>
                <button onClick={onClose} className="btn-primary">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Mic className="h-5 w-5 mr-2" />
                Voice Expense Input
              </h3>
              <button
                type="button"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Voice Input Section */}
              <div className="text-center">
                <div className="mb-4">
                  <button
                    onClick={isListening ? stopListening : startListening}
                    disabled={processing}
                    className={`relative p-8 rounded-full transition-all ${
                      isListening 
                        ? 'bg-red-500 hover:bg-red-600 animate-pulse' 
                        : 'bg-primary-500 hover:bg-primary-600'
                    } text-white disabled:opacity-50`}
                  >
                    {isListening ? (
                      <MicOff className="h-8 w-8" />
                    ) : (
                      <Mic className="h-8 w-8" />
                    )}
                    {isListening && (
                      <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping"></div>
                    )}
                  </button>
                </div>

                <p className="text-gray-600 mb-2">
                  {isListening 
                    ? 'Listening... Speak your expense details' 
                    : 'Click the microphone to start recording'
                  }
                </p>

                <div className="text-sm text-gray-500">
                  <p>Try saying: "I spent 500 on milk yesterday" or "Paid 50 dollars for gas today"</p>
                </div>
              </div>

              {/* Transcript Display */}
              {transcript && (
                <div className="space-y-4">
                  <div>
                    <label className="label">What you said:</label>
                    <div className="p-3 bg-gray-50 rounded-lg border">
                      <p className="text-gray-800">{transcript}</p>
                    </div>
                  </div>

                  {!processing && !analysisResult && (
                    <button
                      onClick={processVoiceInput}
                      className="btn-primary w-full"
                    >
                      <Volume2 className="h-4 w-4 mr-2" />
                      Process Voice Input
                    </button>
                  )}

                  {processing && (
                    <div className="flex items-center justify-center py-4">
                      <LoadingSpinner size="lg" className="mr-3" />
                      <span className="text-gray-600">Processing voice input with AI...</span>
                    </div>
                  )}
                </div>
              )}

              {/* Analysis Results */}
              {analysisResult && editableData && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-sm">
                    {analysisResult.confidence >= 80 ? (
                      <CheckCircle className="h-5 w-5 text-success-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    )}
                    <span className="text-gray-600">
                      Analysis confidence: {analysisResult.confidence}%
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="label">Title</label>
                      <input
                        type="text"
                        className="input"
                        value={editableData.title}
                        onChange={(e) => handleDataChange('title', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Amount</label>
                      <input
                        type="number"
                        step="0.01"
                        className="input"
                        value={editableData.amount}
                        onChange={(e) => handleDataChange('amount', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Date</label>
                      <input
                        type="date"
                        className="input"
                        value={editableData.date}
                        onChange={(e) => handleDataChange('date', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Category</label>
                      <select
                        className="input"
                        value={editableData.category}
                        onChange={(e) => handleDataChange('category', e.target.value)}
                      >
                        {categories.map(cat => (
                          <option key={cat.value} value={cat.value}>
                            {cat.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="label">Description</label>
                    <textarea
                      className="input"
                      rows={2}
                      value={editableData.description}
                      onChange={(e) => handleDataChange('description', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {editableData && (
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                onClick={createExpense}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                Create Expense
              </button>
              <button
                onClick={onClose}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceInput;
