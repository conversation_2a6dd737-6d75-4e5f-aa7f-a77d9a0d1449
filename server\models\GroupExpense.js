const mongoose = require('mongoose');

const groupExpenseSchema = new mongoose.Schema({
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paidBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'food', 'transportation', 'accommodation', 'entertainment', 
      'shopping', 'utilities', 'travel', 'healthcare', 'education',
      'groceries', 'restaurants', 'gas', 'parking', 'other'
    ]
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  splits: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    percentage: {
      type: Number,
      min: 0,
      max: 100
    },
    isPaid: {
      type: Boolean,
      default: false
    },
    paidAt: Date
  }],
  splitMethod: {
    type: String,
    enum: ['equal', 'percentage', 'custom'],
    default: 'equal'
  },
  receipt: {
    type: String, // URL to receipt image
    default: ''
  },
  tags: [{
    type: String,
    trim: true
  }],
  isSettled: {
    type: Boolean,
    default: false
  },
  settledAt: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for better query performance
groupExpenseSchema.index({ group: 1, date: -1 });
groupExpenseSchema.index({ paidBy: 1 });
groupExpenseSchema.index({ 'splits.user': 1 });

// Pre-save middleware to calculate splits
groupExpenseSchema.pre('save', function(next) {
  if (this.splitMethod === 'equal' && this.splits.length > 0) {
    const equalAmount = this.amount / this.splits.length;
    this.splits.forEach(split => {
      split.amount = equalAmount;
      split.percentage = 100 / this.splits.length;
    });
  } else if (this.splitMethod === 'percentage') {
    this.splits.forEach(split => {
      split.amount = (this.amount * split.percentage) / 100;
    });
  }
  
  next();
});

// Method to mark a split as paid
groupExpenseSchema.methods.markSplitPaid = function(userId) {
  const split = this.splits.find(s => s.user.toString() === userId.toString());
  if (split) {
    split.isPaid = true;
    split.paidAt = new Date();
    
    // Check if all splits are paid
    const allPaid = this.splits.every(s => s.isPaid);
    if (allPaid) {
      this.isSettled = true;
      this.settledAt = new Date();
    }
  }
  return this.save();
};

// Static method to get group expense summary
groupExpenseSchema.statics.getGroupSummary = async function(groupId, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        group: mongoose.Types.ObjectId(groupId),
        date: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: '$category',
        total: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { total: -1 }
    }
  ];
  
  return this.aggregate(pipeline);
};

module.exports = mongoose.model('GroupExpense', groupExpenseSchema);
