# PowerShell script to install Node.js and set up Splitify
Write-Host "🚀 Setting up Splitify - Full Stack Application" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Check if Node.js is already installed
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js is already installed: $nodeVersion" -ForegroundColor Green
        $nodeInstalled = $true
    } else {
        $nodeInstalled = $false
    }
} catch {
    $nodeInstalled = $false
}

if (-not $nodeInstalled) {
    Write-Host "📦 Node.js not found. Installing Node.js..." -ForegroundColor Yellow
    
    # Check if Chocolatey is installed
    try {
        choco --version 2>$null
        Write-Host "✅ Chocolatey found. Installing Node.js via Chocolatey..." -ForegroundColor Green
        choco install nodejs -y
    } catch {
        Write-Host "⚠️ Chocolatey not found. Please install Node.js manually:" -ForegroundColor Yellow
        Write-Host "1. Go to https://nodejs.org/" -ForegroundColor White
        Write-Host "2. Download and install the LTS version" -ForegroundColor White
        Write-Host "3. Restart this script after installation" -ForegroundColor White
        
        # Try to open the Node.js website
        try {
            Start-Process "https://nodejs.org/"
        } catch {
            Write-Host "Please manually open: https://nodejs.org/" -ForegroundColor Red
        }
        
        Read-Host "Press Enter after installing Node.js to continue"
    }
}

# Verify Node.js installation
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js installation failed. Please install manually." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📦 Installing project dependencies..." -ForegroundColor Yellow

# Install root dependencies
Write-Host "Installing root dependencies..." -ForegroundColor Cyan
npm install concurrently nodemon --save-dev

# Install server dependencies
Write-Host "Installing server dependencies..." -ForegroundColor Cyan
Set-Location server
npm install

# Install client dependencies  
Write-Host "Installing client dependencies..." -ForegroundColor Cyan
Set-Location ..\client
npm install

# Go back to root
Set-Location ..

Write-Host ""
Write-Host "✅ Installation complete!" -ForegroundColor Green
Write-Host "🚀 Starting Splitify application..." -ForegroundColor Cyan
Write-Host ""

# Start the application
npm run dev
