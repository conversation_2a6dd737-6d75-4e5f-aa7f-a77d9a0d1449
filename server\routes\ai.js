const express = require('express');
const multer = require('multer');
const { body, validationResult } = require('express-validator');
const geminiService = require('../services/geminiService');
const Expense = require('../models/Expense');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept images only
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// @route   POST /api/ai/analyze-bill
// @desc    Analyze bill image using Gemini AI
// @access  Private
router.post('/analyze-bill', auth, upload.single('billImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Analyze the bill image
    const result = await geminiService.analyzeBillImage(
      req.file.buffer,
      req.file.mimetype
    );

    if (!result.success) {
      return res.status(400).json({
        message: result.error,
        details: result.details
      });
    }

    res.json({
      message: 'Bill analyzed successfully',
      data: result.data
    });
  } catch (error) {
    console.error('Bill analysis error:', error);
    res.status(500).json({ message: 'Server error during bill analysis' });
  }
});

// @route   POST /api/ai/create-expense-from-bill
// @desc    Create expense from analyzed bill data
// @access  Private
router.post('/create-expense-from-bill', auth, upload.single('billImage'), [
  body('autoCreate').optional().isBoolean(),
  body('overrides').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Analyze the bill image
    const analysisResult = await geminiService.analyzeBillImage(
      req.file.buffer,
      req.file.mimetype
    );

    if (!analysisResult.success) {
      return res.status(400).json({
        message: analysisResult.error,
        details: analysisResult.details
      });
    }

    const billData = analysisResult.data;
    const overrides = req.body.overrides || {};

    // Merge analyzed data with any user overrides
    const expenseData = {
      title: overrides.title || billData.title,
      description: overrides.description || `${billData.merchant ? `From ${billData.merchant}` : ''}${billData.items ? ` - ${billData.items.join(', ')}` : ''}`.trim(),
      amount: overrides.amount || billData.amount,
      type: 'expense',
      category: overrides.category || billData.category,
      date: overrides.date || billData.date,
      paymentMethod: overrides.paymentMethod || billData.paymentMethod || 'other',
      tags: overrides.tags || (billData.merchant ? [billData.merchant] : []),
      user: req.user.id,
      receipt: '', // In a real app, you'd save the image to cloud storage
    };

    // Create expense if autoCreate is true
    if (req.body.autoCreate) {
      const expense = new Expense(expenseData);
      await expense.save();

      // Update user totals
      await req.user.updateTotals();

      res.status(201).json({
        message: 'Expense created successfully from bill analysis',
        expense,
        analysisData: billData
      });
    } else {
      // Just return the analyzed data for user review
      res.json({
        message: 'Bill analyzed successfully',
        expenseData,
        analysisData: billData
      });
    }
  } catch (error) {
    console.error('Bill expense creation error:', error);
    res.status(500).json({ message: 'Server error during expense creation' });
  }
});

// @route   POST /api/ai/process-voice
// @desc    Process voice input to extract expense information
// @access  Private
router.post('/process-voice', auth, [
  body('voiceText').isString().isLength({ min: 1 }).withMessage('Voice text is required'),
  body('autoCreate').optional().isBoolean(),
  body('overrides').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { voiceText, autoCreate, overrides = {} } = req.body;

    // Process voice input
    const result = await geminiService.processVoiceInput(voiceText);

    if (!result.success) {
      return res.status(400).json({
        message: result.error,
        details: result.details
      });
    }

    const voiceData = result.data;

    // Merge analyzed data with any user overrides
    const expenseData = {
      title: overrides.title || voiceData.title,
      description: overrides.description || voiceData.description || '',
      amount: overrides.amount || voiceData.amount,
      type: 'expense',
      category: overrides.category || voiceData.category,
      date: overrides.date || voiceData.date,
      paymentMethod: overrides.paymentMethod || 'cash',
      tags: overrides.tags || [],
      user: req.user.id,
    };

    // Create expense if autoCreate is true
    if (autoCreate) {
      const expense = new Expense(expenseData);
      await expense.save();

      // Update user totals
      await req.user.updateTotals();

      res.status(201).json({
        message: 'Expense created successfully from voice input',
        expense,
        analysisData: voiceData
      });
    } else {
      // Just return the analyzed data for user review
      res.json({
        message: 'Voice input processed successfully',
        expenseData,
        analysisData: voiceData
      });
    }
  } catch (error) {
    console.error('Voice processing error:', error);
    res.status(500).json({ message: 'Server error during voice processing' });
  }
});

// @route   POST /api/ai/categorize
// @desc    Categorize expense using AI
// @access  Private
router.post('/categorize', auth, [
  body('description').isString().isLength({ min: 1 }).withMessage('Description is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { description } = req.body;
    const category = await geminiService.categorizeExpense(description);

    res.json({
      message: 'Expense categorized successfully',
      category
    });
  } catch (error) {
    console.error('Categorization error:', error);
    res.status(500).json({ message: 'Server error during categorization' });
  }
});

// @route   POST /api/ai/smart-suggestions
// @desc    Get smart expense suggestions based on user history
// @access  Private
router.post('/smart-suggestions', auth, [
  body('context').optional().isString()
], async (req, res) => {
  try {
    const { context } = req.body;

    // Get user's recent expenses for context
    const recentExpenses = await Expense.find({ user: req.user.id })
      .sort({ date: -1 })
      .limit(20)
      .select('title category amount date');

    // Get expense patterns
    const categoryStats = await Expense.aggregate([
      { $match: { user: req.user.id } },
      {
        $group: {
          _id: '$category',
          avgAmount: { $avg: '$amount' },
          count: { $sum: 1 },
          lastExpense: { $max: '$date' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      message: 'Smart suggestions generated',
      suggestions: {
        recentExpenses: recentExpenses.slice(0, 5),
        topCategories: categoryStats.slice(0, 5),
        patterns: {
          mostUsedCategory: categoryStats[0]?._id || 'other-expense',
          averageExpense: categoryStats.reduce((sum, cat) => sum + cat.avgAmount, 0) / categoryStats.length || 0
        }
      }
    });
  } catch (error) {
    console.error('Smart suggestions error:', error);
    res.status(500).json({ message: 'Server error generating suggestions' });
  }
});

module.exports = router;
