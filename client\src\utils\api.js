import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.message || 'An error occurred';
    
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.status >= 400) {
      toast.error(message);
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (data) => api.put('/auth/profile', data),
};

// Expenses API
export const expensesAPI = {
  getExpenses: (params) => api.get('/expenses', { params }),
  createExpense: (data) => api.post('/expenses', data),
  updateExpense: (id, data) => api.put(`/expenses/${id}`, data),
  deleteExpense: (id) => api.delete(`/expenses/${id}`),
  getSummary: (params) => api.get('/expenses/summary', { params }),
};

// Groups API
export const groupsAPI = {
  getGroups: () => api.get('/groups'),
  createGroup: (data) => api.post('/groups', data),
  updateGroup: (id, data) => api.put(`/groups/${id}`, data),
  addMember: (id, data) => api.post(`/groups/${id}/members`, data),
  getGroupExpenses: (id) => api.get(`/groups/${id}/expenses`),
  addGroupExpense: (id, data) => api.post(`/groups/${id}/expenses`, data),
  getBalances: (id) => api.get(`/groups/${id}/balances`),
};

// Users API
export const usersAPI = {
  searchUsers: (query) => api.get('/users/search', { params: { q: query } }),
  getDashboard: () => api.get('/users/dashboard'),
};

// AI API
export const aiAPI = {
  analyzeBill: (imageFile) => {
    const formData = new FormData();
    formData.append('billImage', imageFile);
    return api.post('/ai/analyze-bill', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  createExpenseFromBill: (imageFile, autoCreate = false, overrides = {}) => {
    const formData = new FormData();
    formData.append('billImage', imageFile);
    formData.append('autoCreate', autoCreate);
    formData.append('overrides', JSON.stringify(overrides));
    return api.post('/ai/create-expense-from-bill', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  processVoice: (voiceText, autoCreate = false, overrides = {}) =>
    api.post('/ai/process-voice', { voiceText, autoCreate, overrides }),
  categorizeExpense: (description) =>
    api.post('/ai/categorize', { description }),
  getSmartSuggestions: (context) =>
    api.post('/ai/smart-suggestions', { context }),
};

export default api;
