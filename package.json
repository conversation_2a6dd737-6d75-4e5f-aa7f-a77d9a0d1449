{"name": "splitify", "version": "1.0.0", "description": "A comprehensive expense tracking and splitting application", "main": "server/index.js", "scripts": {"dev": "node app.js", "start": "node app.js", "server": "node app.js", "install-deps": "npm install express cors", "build": "echo 'No build needed for this version'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["expense", "tracking", "splitting", "mern", "dashboard"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}