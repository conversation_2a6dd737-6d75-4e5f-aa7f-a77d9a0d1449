{"name": "splitify", "version": "1.0.0", "description": "A comprehensive expense tracking and splitting application", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "start": "cd server && npm start"}, "keywords": ["expense", "tracking", "splitting", "mern", "dashboard"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}