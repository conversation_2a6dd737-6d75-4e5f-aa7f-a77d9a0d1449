// Splitify - Complete Express.js Application
const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Sample data (in production, this would be in MongoDB)
let users = {
  "<EMAIL>": {
    id: "1",
    name: "Demo User",
    email: "<EMAIL>",
    password: "password123",
    totalIncome: 5000,
    totalExpenses: 3200
  }
};

let expenses = [
  {
    id: "1",
    userId: "1",
    title: "Grocery Shopping",
    amount: 85.50,
    category: "food",
    date: "2024-01-15",
    type: "expense",
    description: "Weekly groceries from Walmart",
    paymentMethod: "credit-card",
    tags: ["walmart", "weekly"]
  },
  {
    id: "2",
    userId: "1", 
    title: "Gas Station",
    amount: 45.00,
    category: "transportation",
    date: "2024-01-14",
    type: "expense",
    description: "Fuel for car",
    paymentMethod: "debit-card",
    tags: ["shell", "fuel"]
  },
  {
    id: "3",
    userId: "1",
    title: "Freelance Payment",
    amount: 1200.00,
    category: "freelance",
    date: "2024-01-13",
    type: "income",
    description: "Web development project",
    paymentMethod: "bank-transfer",
    tags: ["freelance", "web-dev"]
  }
];

let groups = [
  {
    id: "1",
    name: "Weekend Trip",
    description: "Expenses for our weekend getaway",
    totalExpenses: 450.00,
    members: ["Demo User", "John Doe", "Jane Smith"],
    currency: "USD"
  }
];

// Simulate Gemini AI responses
function simulateBillAnalysis() {
  const samples = [
    {
      title: "Grocery Store Receipt",
      amount: 85.50,
      date: new Date().toISOString().split('T')[0],
      category: "food",
      merchant: "Walmart Supercenter",
      items: ["Milk", "Bread", "Eggs", "Apples"],
      paymentMethod: "credit-card",
      confidence: 95
    },
    {
      title: "Gas Station",
      amount: 45.00,
      date: new Date().toISOString().split('T')[0],
      category: "transportation",
      merchant: "Shell Gas Station",
      items: ["Regular Gasoline"],
      paymentMethod: "debit-card",
      confidence: 92
    }
  ];
  return samples[Math.floor(Math.random() * samples.length)];
}

function simulateVoiceProcessing(voiceText) {
  const text = voiceText.toLowerCase();
  
  let amount = 0;
  if (text.includes("500") || text.includes("five hundred")) amount = 500;
  else if (text.includes("50") || text.includes("fifty")) amount = 50;
  else if (text.includes("120")) amount = 120;
  else if (text.includes("25")) amount = 25;
  
  let category = "other-expense";
  if (text.includes("milk") || text.includes("grocery") || text.includes("food")) category = "food";
  else if (text.includes("gas") || text.includes("fuel")) category = "transportation";
  else if (text.includes("coffee") || text.includes("restaurant")) category = "food";
  
  let date = new Date().toISOString().split('T')[0];
  if (text.includes("yesterday")) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    date = yesterday.toISOString().split('T')[0];
  }
  
  let title = "Expense";
  if (text.includes("milk")) title = "Milk purchase";
  else if (text.includes("gas")) title = "Gas station";
  else if (text.includes("coffee")) title = "Coffee shop";
  else if (text.includes("grocery")) title = "Grocery shopping";
  
  return {
    title,
    amount,
    date,
    category,
    description: `Parsed from voice: ${voiceText}`,
    confidence: Math.floor(Math.random() * 10) + 85
  };
}

// Routes

// Serve main application
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Authentication
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (users[email] && users[email].password === password) {
    res.json({
      success: true,
      token: "demo-jwt-token",
      user: {
        id: users[email].id,
        name: users[email].name,
        email: users[email].email,
        totalIncome: users[email].totalIncome,
        totalExpenses: users[email].totalExpenses
      }
    });
  } else {
    res.status(401).json({ success: false, message: "Invalid credentials" });
  }
});

// Dashboard
app.get('/api/dashboard', (req, res) => {
  const userExpenses = expenses.filter(e => e.userId === "1");
  
  // Calculate category breakdown
  const categoryBreakdown = {};
  userExpenses.forEach(expense => {
    if (expense.type === 'expense') {
      categoryBreakdown[expense.category] = (categoryBreakdown[expense.category] || 0) + expense.amount;
    }
  });
  
  const categoryList = Object.entries(categoryBreakdown).map(([category, total]) => ({
    _id: category,
    total
  }));
  
  const totalIncome = userExpenses.filter(e => e.type === 'income').reduce((sum, e) => sum + e.amount, 0);
  const totalExpenses = userExpenses.filter(e => e.type === 'expense').reduce((sum, e) => sum + e.amount, 0);
  
  res.json({
    user: users["<EMAIL>"],
    recentExpenses: userExpenses.slice(-5),
    monthlyStats: {
      income: totalIncome,
      expenses: totalExpenses,
      balance: totalIncome - totalExpenses
    },
    categoryBreakdown: categoryList,
    groups: groups
  });
});

// Expenses
app.get('/api/expenses', (req, res) => {
  const userExpenses = expenses.filter(e => e.userId === "1");
  res.json({ 
    expenses: userExpenses,
    pagination: {
      current: 1,
      pages: 1,
      total: userExpenses.length
    }
  });
});

app.post('/api/expenses', (req, res) => {
  const newExpense = {
    id: String(expenses.length + 1),
    userId: "1",
    ...req.body,
    date: req.body.date || new Date().toISOString().split('T')[0]
  };
  
  expenses.push(newExpense);
  
  res.json({
    success: true,
    expense: newExpense
  });
});

// AI Features
app.post('/api/ai/analyze-bill', (req, res) => {
  // Simulate processing delay
  setTimeout(() => {
    const result = simulateBillAnalysis();
    res.json({
      success: true,
      message: "Bill analyzed successfully",
      data: result
    });
  }, 1000);
});

app.post('/api/ai/process-voice', (req, res) => {
  const { voiceText } = req.body;
  
  // Simulate processing delay
  setTimeout(() => {
    const result = simulateVoiceProcessing(voiceText);
    res.json({
      success: true,
      message: "Voice input processed successfully",
      analysisData: result
    });
  }, 1000);
});

// Groups
app.get('/api/groups', (req, res) => {
  res.json({ groups });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log('\n🚀 Splitify Server Started!');
  console.log('=' * 50);
  console.log(`🌐 Application: http://localhost:${PORT}`);
  console.log(`🔧 API Endpoint: http://localhost:${PORT}/api`);
  console.log(`🤖 AI Features: Simulated Gemini API`);
  console.log('=' * 50);
  console.log('📋 Available endpoints:');
  console.log('   • POST /api/auth/login');
  console.log('   • GET  /api/dashboard');
  console.log('   • GET  /api/expenses');
  console.log('   • POST /api/expenses');
  console.log('   • POST /api/ai/analyze-bill');
  console.log('   • POST /api/ai/process-voice');
  console.log('   • GET  /api/groups');
  console.log('=' * 50);
  console.log('📧 Demo Login: <EMAIL>');
  console.log('🔑 Password: password123');
  console.log('=' * 50);
  console.log('\n✨ Features available:');
  console.log('   • User Authentication');
  console.log('   • Interactive Dashboard');
  console.log('   • Expense Management');
  console.log('   • AI Bill Scanner');
  console.log('   • AI Voice Input');
  console.log('   • Group Management');
  console.log('\n🎯 Ready for testing!\n');
});

module.exports = app;
