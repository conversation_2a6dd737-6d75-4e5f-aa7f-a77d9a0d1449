const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Checking Development Environment...\n');

// Check Node.js
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`✅ Node.js: ${nodeVersion}`);
} catch (error) {
  console.log('❌ Node.js: Not installed');
  console.log('   Please install from: https://nodejs.org/');
  process.exit(1);
}

// Check npm
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`✅ npm: ${npmVersion}`);
} catch (error) {
  console.log('❌ npm: Not available');
  process.exit(1);
}

// Check MongoDB
try {
  execSync('mongod --version', { encoding: 'utf8', stdio: 'ignore' });
  console.log('✅ MongoDB: Installed');
} catch (error) {
  console.log('⚠️  MongoDB: Not found in PATH');
  console.log('   You can still use MongoDB Atlas or install locally');
}

// Check project structure
const requiredFiles = [
  'package.json',
  'server/package.json',
  'client/package.json',
  'server/index.js',
  'client/src/index.js',
  'server/.env'
];

console.log('\n📁 Checking Project Structure:');
requiredFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing`);
  }
});

// Check environment variables
console.log('\n🔧 Environment Configuration:');
const envPath = path.join(__dirname, 'server', '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('✅ .env file exists');
  
  if (envContent.includes('GEMINI_API_KEY')) {
    console.log('✅ Gemini API key configured');
  } else {
    console.log('⚠️  Gemini API key not found in .env');
  }
  
  if (envContent.includes('MONGODB_URI')) {
    console.log('✅ MongoDB URI configured');
  } else {
    console.log('⚠️  MongoDB URI not found in .env');
  }
} else {
  console.log('❌ .env file missing');
}

console.log('\n🚀 Ready to start Splitify!');
console.log('Run: npm run dev (from root directory)');
console.log('Or use: setup-and-run.bat for full setup\n');
