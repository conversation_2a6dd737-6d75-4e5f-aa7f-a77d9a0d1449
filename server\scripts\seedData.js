const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Expense = require('../models/Expense');
const Group = require('../models/Group');

const seedData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/splitify');
    console.log('Connected to MongoDB');

    // Clear existing data
    await User.deleteMany({});
    await Expense.deleteMany({});
    await Group.deleteMany({});
    console.log('Cleared existing data');

    // Create demo user
    const hashedPassword = await bcrypt.hash('password123', 10);
    const demoUser = new User({
      name: 'Demo User',
      email: '<EMAIL>',
      password: hashedPassword,
      totalIncome: 5000,
      totalExpenses: 3200,
      preferences: {
        currency: 'USD',
        theme: 'light',
        notifications: {
          email: true,
          push: true
        }
      }
    });
    await demoUser.save();
    console.log('Created demo user');

    // Create sample expenses
    const sampleExpenses = [
      {
        user: demoUser._id,
        title: 'Grocery Shopping',
        description: 'Weekly groceries from Walmart',
        amount: 85.50,
        type: 'expense',
        category: 'food',
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        paymentMethod: 'credit-card',
        tags: ['walmart', 'weekly']
      },
      {
        user: demoUser._id,
        title: 'Gas Station',
        description: 'Fuel for car',
        amount: 45.00,
        type: 'expense',
        category: 'transportation',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        paymentMethod: 'debit-card',
        tags: ['shell', 'fuel']
      },
      {
        user: demoUser._id,
        title: 'Freelance Payment',
        description: 'Web development project',
        amount: 1200.00,
        type: 'income',
        category: 'freelance',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        paymentMethod: 'bank-transfer',
        tags: ['freelance', 'web-dev']
      },
      {
        user: demoUser._id,
        title: 'Coffee Shop',
        description: 'Morning coffee',
        amount: 5.50,
        type: 'expense',
        category: 'food',
        date: new Date(),
        paymentMethod: 'cash',
        tags: ['starbucks', 'coffee']
      },
      {
        user: demoUser._id,
        title: 'Netflix Subscription',
        description: 'Monthly streaming service',
        amount: 15.99,
        type: 'expense',
        category: 'entertainment',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        paymentMethod: 'credit-card',
        tags: ['netflix', 'subscription']
      }
    ];

    await Expense.insertMany(sampleExpenses);
    console.log('Created sample expenses');

    // Create a sample group
    const sampleGroup = new Group({
      name: 'Weekend Trip',
      description: 'Expenses for our weekend getaway',
      creator: demoUser._id,
      members: [{
        user: demoUser._id,
        role: 'admin',
        joinedAt: new Date()
      }],
      totalExpenses: 450.00,
      currency: 'USD',
      settings: {
        allowMemberAddExpenses: true,
        requireApproval: false,
        splitMethod: 'equal'
      }
    });
    await sampleGroup.save();
    console.log('Created sample group');

    // Update user's groups
    demoUser.groups.push(sampleGroup._id);
    await demoUser.save();

    console.log('✅ Sample data created successfully!');
    console.log('📧 Demo login: <EMAIL>');
    console.log('🔑 Password: password123');
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
};

seedData();
