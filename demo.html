<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splitify - AI-Powered Expense Tracking Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-chart-pie text-3xl"></i>
                    <h1 class="text-3xl font-bold">Splitify</h1>
                </div>
                <div class="text-sm">
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                        <i class="fas fa-robot mr-1"></i>
                        AI-Powered
                    </span>
                </div>
            </div>
            <p class="mt-2 text-lg opacity-90">Smart Expense Tracking with Gemini AI Integration</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4">
            <div class="flex space-x-8 py-4">
                <a href="#dashboard" class="text-blue-600 border-b-2 border-blue-600 pb-2 font-medium">Dashboard</a>
                <a href="#expenses" class="text-gray-600 hover:text-blue-600 pb-2">Expenses</a>
                <a href="#ai-features" class="text-gray-600 hover:text-blue-600 pb-2">AI Features</a>
                <a href="#groups" class="text-gray-600 hover:text-blue-600 pb-2">Groups</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Dashboard Section -->
        <section id="dashboard" class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Dashboard Overview</h2>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i class="fas fa-arrow-up text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Total Income</p>
                            <p class="text-2xl font-bold text-gray-900">$12,450</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-red-100 rounded-lg">
                            <i class="fas fa-arrow-down text-red-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Total Expenses</p>
                            <p class="text-2xl font-bold text-gray-900">$8,230</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i class="fas fa-wallet text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Balance</p>
                            <p class="text-2xl font-bold text-green-600">$4,220</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-lg">
                            <i class="fas fa-users text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Active Groups</p>
                            <p class="text-2xl font-bold text-gray-900">3</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">Monthly Expenses</h3>
                    <canvas id="expenseChart" width="400" height="200"></canvas>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">Category Breakdown</h3>
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </section>

        <!-- AI Features Section -->
        <section id="ai-features" class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">
                <i class="fas fa-robot mr-2 text-blue-600"></i>
                AI-Powered Features
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Bill Scanner -->
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-camera text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Smart Bill Scanner</h3>
                        <p class="text-gray-600 mb-4">Upload or take a photo of any receipt. AI extracts amount, date, merchant, and category automatically.</p>
                        
                        <button onclick="showBillScanner()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-camera mr-2"></i>
                            Try Bill Scanner
                        </button>
                        
                        <div class="mt-4 text-sm text-gray-500">
                            <p><strong>Confidence:</strong> 95% accuracy</p>
                            <p><strong>Supports:</strong> Receipts, bills, invoices</p>
                        </div>
                    </div>
                </div>

                <!-- Voice Input -->
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-microphone text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Voice Expense Input</h3>
                        <p class="text-gray-600 mb-4">Simply speak your expense details. "I spent 500 on milk yesterday" - AI understands natural language.</p>
                        
                        <button onclick="showVoiceInput()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-microphone mr-2"></i>
                            Try Voice Input
                        </button>
                        
                        <div class="mt-4 text-sm text-gray-500">
                            <p><strong>Languages:</strong> English, Hindi, Spanish</p>
                            <p><strong>Accuracy:</strong> 92% speech recognition</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Demo Results -->
            <div id="ai-results" class="mt-8 hidden">
                <h3 class="text-lg font-semibold mb-4">AI Extraction Results</h3>
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Title</label>
                            <input type="text" id="ai-title" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Amount</label>
                            <input type="text" id="ai-amount" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Category</label>
                            <input type="text" id="ai-category" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date</label>
                            <input type="text" id="ai-date" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            <i class="fas fa-save mr-2"></i>
                            Save Expense
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recent Expenses -->
        <section id="expenses" class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Expenses</h2>
            
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">Latest Transactions</h3>
                        <div class="flex space-x-2">
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                <i class="fas fa-microphone mr-2"></i>
                                Voice Add
                            </button>
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-camera mr-2"></i>
                                Scan Bill
                            </button>
                            <button class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                                <i class="fas fa-plus mr-2"></i>
                                Add Manual
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="divide-y divide-gray-200">
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-utensils text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Grocery Shopping</p>
                                <p class="text-sm text-gray-500">Food • Today • AI Scanned</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-red-600">-$85.50</p>
                            <p class="text-sm text-gray-500">Credit Card</p>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-gas-pump text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Gas Station</p>
                                <p class="text-sm text-gray-500">Transportation • Yesterday • Voice Input</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-red-600">-$45.00</p>
                            <p class="text-sm text-gray-500">Debit Card</p>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-briefcase text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Freelance Payment</p>
                                <p class="text-sm text-gray-500">Income • 2 days ago • Manual</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-green-600">+$1,200.00</p>
                            <p class="text-sm text-gray-500">Bank Transfer</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Groups Section -->
        <section id="groups" class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Expense Groups</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold">Weekend Trip</h3>
                        <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">Active</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">3 members</p>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>Total Expenses:</span>
                            <span class="font-medium">$450.00</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>Your Share:</span>
                            <span class="font-medium text-red-600">$150.00</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold">Roommate Expenses</h3>
                        <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Ongoing</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">2 members</p>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>Total Expenses:</span>
                            <span class="font-medium">$1,200.00</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>You Owe:</span>
                            <span class="font-medium text-red-600">$75.00</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold">Office Lunch</h3>
                        <span class="text-sm bg-gray-100 text-gray-800 px-2 py-1 rounded-full">Settled</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">5 members</p>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>Total Expenses:</span>
                            <span class="font-medium">$125.00</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>Your Share:</span>
                            <span class="font-medium text-green-600">Paid</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- Bill Scanner Modal -->
    <div id="billScannerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">AI Bill Scanner</h3>
                    <button onclick="closeBillScanner()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-center py-8">
                    <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-animation">
                        <i class="fas fa-camera text-blue-600 text-3xl"></i>
                    </div>
                    <p class="text-gray-600 mb-4">AI is analyzing your bill...</p>
                    <div class="bg-gray-200 rounded-full h-2 mb-4">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                    </div>
                    <button onclick="simulateBillScan()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                        Simulate Bill Scan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Input Modal -->
    <div id="voiceInputModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Voice Expense Input</h3>
                    <button onclick="closeVoiceInput()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-center py-8">
                    <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-animation">
                        <i class="fas fa-microphone text-green-600 text-3xl"></i>
                    </div>
                    <p class="text-gray-600 mb-4">Listening... Speak your expense details</p>
                    <p class="text-sm text-gray-500 mb-4">Try: "I spent 500 on milk yesterday"</p>
                    <button onclick="simulateVoiceInput()" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">
                        Simulate Voice Input
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Expense Chart
            const expenseCtx = document.getElementById('expenseChart').getContext('2d');
            new Chart(expenseCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Expenses',
                        data: [1200, 1900, 800, 1500, 2000, 1800],
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Income',
                        data: [2000, 2200, 1800, 2100, 2400, 2300],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Food', 'Transportation', 'Entertainment', 'Shopping', 'Utilities'],
                    datasets: [{
                        data: [30, 20, 15, 25, 10],
                        backgroundColor: [
                            '#ef4444',
                            '#3b82f6',
                            '#8b5cf6',
                            '#f59e0b',
                            '#10b981'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        });

        // Modal Functions
        function showBillScanner() {
            document.getElementById('billScannerModal').classList.remove('hidden');
        }

        function closeBillScanner() {
            document.getElementById('billScannerModal').classList.add('hidden');
        }

        function showVoiceInput() {
            document.getElementById('voiceInputModal').classList.remove('hidden');
        }

        function closeVoiceInput() {
            document.getElementById('voiceInputModal').classList.add('hidden');
        }

        function simulateBillScan() {
            closeBillScanner();
            showAIResults('Grocery Store Receipt', '$85.50', 'Food', '2024-01-15');
        }

        function simulateVoiceInput() {
            closeVoiceInput();
            showAIResults('Gas Station', '$45.00', 'Transportation', '2024-01-14');
        }

        function showAIResults(title, amount, category, date) {
            document.getElementById('ai-title').value = title;
            document.getElementById('ai-amount').value = amount;
            document.getElementById('ai-category').value = category;
            document.getElementById('ai-date').value = date;
            document.getElementById('ai-results').classList.remove('hidden');
            
            // Scroll to results
            document.getElementById('ai-results').scrollIntoView({ behavior: 'smooth' });
        }

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
