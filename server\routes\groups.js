const express = require('express');
const { body, validationResult } = require('express-validator');
const Group = require('../models/Group');
const GroupExpense = require('../models/GroupExpense');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/groups
// @desc    Get user's groups
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const groups = await Group.find({
      'members.user': req.user.id,
      isActive: true
    })
    .populate('creator', 'name email')
    .populate('members.user', 'name email')
    .sort({ updatedAt: -1 });

    res.json({ groups });
  } catch (error) {
    console.error('Get groups error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/groups
// @desc    Create a new group
// @access  Private
router.post('/', auth, [
  body('name').trim().isLength({ min: 1, max: 100 }).withMessage('Group name is required'),
  body('description').optional().trim().isLength({ max: 500 }),
  body('currency').optional().isIn(['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const group = new Group({
      name: req.body.name,
      description: req.body.description,
      creator: req.user.id,
      currency: req.body.currency || 'USD',
      members: [{
        user: req.user.id,
        role: 'admin'
      }]
    });

    await group.save();

    // Add group to user's groups array
    await User.findByIdAndUpdate(req.user.id, {
      $push: { groups: group._id }
    });

    await group.populate('creator', 'name email');
    await group.populate('members.user', 'name email');

    res.status(201).json({
      message: 'Group created successfully',
      group
    });
  } catch (error) {
    console.error('Create group error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/groups/:id
// @desc    Update group details
// @access  Private
router.put('/:id', auth, [
  body('name').optional().trim().isLength({ min: 1, max: 100 }),
  body('description').optional().trim().isLength({ max: 500 }),
  body('settings.allowMemberAddExpenses').optional().isBoolean(),
  body('settings.requireApproval').optional().isBoolean(),
  body('settings.splitMethod').optional().isIn(['equal', 'percentage', 'custom'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is admin
    const member = group.members.find(m => m.user.toString() === req.user.id);
    if (!member || member.role !== 'admin') {
      return res.status(403).json({ message: 'Only group admins can update group settings' });
    }

    Object.assign(group, req.body);
    await group.save();

    await group.populate('creator', 'name email');
    await group.populate('members.user', 'name email');

    res.json({
      message: 'Group updated successfully',
      group
    });
  } catch (error) {
    console.error('Update group error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/groups/:id/members
// @desc    Add member to group
// @access  Private
router.post('/:id/members', auth, [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is member of the group
    const isMember = group.members.some(m => m.user.toString() === req.user.id);
    if (!isMember) {
      return res.status(403).json({ message: 'You are not a member of this group' });
    }

    // Find user to add
    const userToAdd = await User.findOne({ email: req.body.email });
    if (!userToAdd) {
      return res.status(404).json({ message: 'User not found with this email' });
    }

    // Check if user is already a member
    const isAlreadyMember = group.members.some(m => m.user.toString() === userToAdd._id.toString());
    if (isAlreadyMember) {
      return res.status(400).json({ message: 'User is already a member of this group' });
    }

    // Add member to group
    group.members.push({
      user: userToAdd._id,
      role: 'member'
    });

    await group.save();

    // Add group to user's groups array
    await User.findByIdAndUpdate(userToAdd._id, {
      $push: { groups: group._id }
    });

    await group.populate('members.user', 'name email');

    res.json({
      message: 'Member added successfully',
      group
    });
  } catch (error) {
    console.error('Add member error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/groups/:id/expenses
// @desc    Get group expenses
// @access  Private
router.get('/:id/expenses', auth, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is member of the group
    const isMember = group.members.some(m => m.user.toString() === req.user.id);
    if (!isMember) {
      return res.status(403).json({ message: 'You are not a member of this group' });
    }

    const expenses = await GroupExpense.find({ group: req.params.id })
      .populate('paidBy', 'name email')
      .populate('splits.user', 'name email')
      .populate('createdBy', 'name email')
      .sort({ date: -1 });

    res.json({ expenses });
  } catch (error) {
    console.error('Get group expenses error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/groups/:id/expenses
// @desc    Add expense to group
// @access  Private
router.post('/:id/expenses', auth, [
  body('title').trim().isLength({ min: 1, max: 100 }).withMessage('Title is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be positive'),
  body('category').isString().withMessage('Category is required'),
  body('paidBy').isMongoId().withMessage('Valid paidBy user ID is required'),
  body('splits').isArray({ min: 1 }).withMessage('At least one split is required'),
  body('splits.*.user').isMongoId().withMessage('Valid user ID required for each split'),
  body('splitMethod').optional().isIn(['equal', 'percentage', 'custom'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is member and can add expenses
    const member = group.members.find(m => m.user.toString() === req.user.id);
    if (!member) {
      return res.status(403).json({ message: 'You are not a member of this group' });
    }

    if (!group.settings.allowMemberAddExpenses && member.role !== 'admin') {
      return res.status(403).json({ message: 'Only admins can add expenses to this group' });
    }

    const expense = new GroupExpense({
      ...req.body,
      group: req.params.id,
      createdBy: req.user.id,
      date: req.body.date ? new Date(req.body.date) : new Date()
    });

    await expense.save();

    // Update group total expenses
    group.totalExpenses += expense.amount;
    group.expenses.push(expense._id);
    await group.save();

    await expense.populate('paidBy', 'name email');
    await expense.populate('splits.user', 'name email');
    await expense.populate('createdBy', 'name email');

    // Emit real-time update
    const { io } = require('../index');
    io.to(req.params.id).emit('expense-added', expense);

    res.status(201).json({
      message: 'Expense added successfully',
      expense
    });
  } catch (error) {
    console.error('Add group expense error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/groups/:id/balances
// @desc    Get group balances and settlements
// @access  Private
router.get('/:id/balances', auth, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Check if user is member of the group
    const isMember = group.members.some(m => m.user.toString() === req.user.id);
    if (!isMember) {
      return res.status(403).json({ message: 'You are not a member of this group' });
    }

    const balances = await group.calculateBalances();
    const settlements = await group.getSettlements();

    // Populate user details for settlements
    const populatedSettlements = await Promise.all(
      settlements.map(async (settlement) => {
        const fromUser = await User.findById(settlement.from).select('name email');
        const toUser = await User.findById(settlement.to).select('name email');
        return {
          ...settlement,
          fromUser,
          toUser
        };
      })
    );

    res.json({
      balances,
      settlements: populatedSettlements
    });
  } catch (error) {
    console.error('Get balances error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
