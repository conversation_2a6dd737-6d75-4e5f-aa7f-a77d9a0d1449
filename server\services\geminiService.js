const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
  }

  // Analyze bill/receipt image to extract expense information
  async analyzeBillImage(imageBuffer, mimeType) {
    try {
      const prompt = `
        Analyze this bill/receipt image and extract the following information in JSON format:
        {
          "title": "Brief description of the expense",
          "amount": "Total amount as a number",
          "date": "Date in YYYY-MM-DD format (if not found, use today's date)",
          "category": "One of: food, transportation, housing, utilities, healthcare, entertainment, shopping, education, travel, insurance, debt, savings, charity, other-expense",
          "merchant": "Store/merchant name",
          "items": ["list of items purchased"],
          "paymentMethod": "One of: cash, credit-card, debit-card, bank-transfer, digital-wallet, other",
          "confidence": "Confidence level from 0-100"
        }

        Rules:
        - If amount is unclear, set confidence low
        - For category, choose the most appropriate from the list
        - If date is not clear, use today's date
        - Extract merchant name from the receipt
        - List main items if visible
        - Be conservative with confidence scoring

        Return only valid JSON, no additional text.
      `;

      const imagePart = {
        inlineData: {
          data: imageBuffer.toString('base64'),
          mimeType: mimeType
        }
      };

      const result = await this.model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
      const expenseData = JSON.parse(cleanedText);

      // Validate required fields
      if (!expenseData.amount || !expenseData.title) {
        throw new Error('Could not extract essential information from the bill');
      }

      return {
        success: true,
        data: expenseData
      };
    } catch (error) {
      console.error('Bill analysis error:', error);
      return {
        success: false,
        error: 'Failed to analyze bill image',
        details: error.message
      };
    }
  }

  // Process voice input to extract expense information
  async processVoiceInput(voiceText) {
    try {
      const prompt = `
        Analyze this voice input about an expense and extract information in JSON format:
        "${voiceText}"

        Extract:
        {
          "title": "Brief description of the expense",
          "amount": "Amount as a number (extract from text like '500', 'five hundred', etc.)",
          "date": "Date in YYYY-MM-DD format (parse relative dates like 'yesterday', 'today', 'last week')",
          "category": "One of: food, transportation, housing, utilities, healthcare, entertainment, shopping, education, travel, insurance, debt, savings, charity, other-expense",
          "description": "More detailed description if available",
          "confidence": "Confidence level from 0-100"
        }

        Rules:
        - Parse natural language amounts (e.g., "five hundred" = 500)
        - Handle relative dates (yesterday = previous day, today = current date)
        - Categorize based on context (milk = food, gas = transportation, etc.)
        - If unclear, set confidence low
        - Use current date as reference for relative dates

        Examples:
        - "I spent 500 on milk yesterday" → amount: 500, category: food, date: yesterday
        - "Paid fifty dollars for gas today" → amount: 50, category: transportation, date: today
        - "Bought groceries for 120 rupees last Monday" → amount: 120, category: food, date: last Monday

        Return only valid JSON, no additional text.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
      const expenseData = JSON.parse(cleanedText);

      // Validate required fields
      if (!expenseData.amount || !expenseData.title) {
        throw new Error('Could not extract essential information from voice input');
      }

      // Process relative dates
      expenseData.date = this.processRelativeDate(expenseData.date);

      return {
        success: true,
        data: expenseData
      };
    } catch (error) {
      console.error('Voice processing error:', error);
      return {
        success: false,
        error: 'Failed to process voice input',
        details: error.message
      };
    }
  }

  // Helper function to process relative dates
  processRelativeDate(dateString) {
    const today = new Date();
    const dateStr = dateString.toLowerCase();

    if (dateStr.includes('today')) {
      return today.toISOString().split('T')[0];
    } else if (dateStr.includes('yesterday')) {
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday.toISOString().split('T')[0];
    } else if (dateStr.includes('last week')) {
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);
      return lastWeek.toISOString().split('T')[0];
    } else if (dateStr.includes('last month')) {
      const lastMonth = new Date(today);
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      return lastMonth.toISOString().split('T')[0];
    } else {
      // Try to parse as regular date, fallback to today
      try {
        const parsedDate = new Date(dateString);
        if (!isNaN(parsedDate.getTime())) {
          return parsedDate.toISOString().split('T')[0];
        }
      } catch (e) {
        // Fallback to today
      }
      return today.toISOString().split('T')[0];
    }
  }

  // Categorize expense based on description
  async categorizeExpense(description) {
    try {
      const prompt = `
        Categorize this expense description into one of these categories:
        food, transportation, housing, utilities, healthcare, entertainment, shopping, education, travel, insurance, debt, savings, charity, other-expense

        Description: "${description}"

        Return only the category name, nothing else.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const category = response.text().trim().toLowerCase();

      const validCategories = [
        'food', 'transportation', 'housing', 'utilities', 'healthcare', 
        'entertainment', 'shopping', 'education', 'travel', 'insurance',
        'debt', 'savings', 'charity', 'other-expense'
      ];

      return validCategories.includes(category) ? category : 'other-expense';
    } catch (error) {
      console.error('Categorization error:', error);
      return 'other-expense';
    }
  }
}

module.exports = new GeminiService();
