// Splitify Frontend JavaScript
let currentUser = null;
let expenses = [];
let dashboardData = null;

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Splitify App Initialized');
    
    // Check if user is logged in
    const token = localStorage.getItem('splitify_token');
    if (token) {
        showMainApp();
        loadDashboard();
    } else {
        showLoginModal();
    }
});

// Authentication
function showLoginModal() {
    document.getElementById('loginModal').classList.remove('hidden');
    document.getElementById('mainApp').classList.add('hidden');
}

function showMainApp() {
    document.getElementById('loginModal').classList.add('hidden');
    document.getElementById('mainApp').classList.remove('hidden');
}

// Login form handler
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            localStorage.setItem('splitify_token', data.token);
            currentUser = data.user;
            document.getElementById('userName').textContent = data.user.name;
            showMainApp();
            loadDashboard();
            showNotification('Login successful!', 'success');
        } else {
            showNotification('Login failed: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');
    }
});

function logout() {
    localStorage.removeItem('splitify_token');
    currentUser = null;
    showLoginModal();
    showNotification('Logged out successfully', 'success');
}

// Navigation
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.className = 'nav-link text-gray-600 hover:text-blue-600 pb-2';
    });
    event.target.className = 'nav-link text-blue-600 border-b-2 border-blue-600 pb-2 font-medium';
    
    // Load section data
    if (sectionId === 'expenses') {
        loadExpenses();
    } else if (sectionId === 'groups') {
        loadGroups();
    }
}

// Dashboard functions
async function loadDashboard() {
    try {
        const response = await fetch('/api/dashboard');
        dashboardData = await response.json();
        
        updateDashboardStats();
        loadExpenses();
        initializeCharts();
        
    } catch (error) {
        console.error('Dashboard load error:', error);
        showNotification('Failed to load dashboard', 'error');
    }
}

function updateDashboardStats() {
    if (!dashboardData) return;
    
    document.getElementById('totalIncome').textContent = '$' + dashboardData.monthlyStats.income.toLocaleString();
    document.getElementById('totalExpenses').textContent = '$' + dashboardData.monthlyStats.expenses.toLocaleString();
    document.getElementById('balance').textContent = '$' + dashboardData.monthlyStats.balance.toLocaleString();
    document.getElementById('activeGroups').textContent = dashboardData.groups.length;
    
    // Update recent expenses
    const recentList = document.getElementById('recentExpensesList');
    recentList.innerHTML = '';
    
    dashboardData.recentExpenses.forEach(expense => {
        const expenseDiv = document.createElement('div');
        expenseDiv.className = 'px-6 py-4 flex items-center justify-between';
        expenseDiv.innerHTML = `
            <div class="flex items-center">
                <div class="w-10 h-10 bg-${expense.type === 'income' ? 'green' : 'red'}-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-${getExpenseIcon(expense.category)} text-${expense.type === 'income' ? 'green' : 'red'}-600"></i>
                </div>
                <div class="ml-4">
                    <p class="font-medium">${expense.title}</p>
                    <p class="text-sm text-gray-500">${expense.category} • ${expense.date}</p>
                </div>
            </div>
            <div class="text-right">
                <p class="font-medium text-${expense.type === 'income' ? 'green' : 'red'}-600">
                    ${expense.type === 'income' ? '+' : '-'}$${expense.amount.toLocaleString()}
                </p>
            </div>
        `;
        recentList.appendChild(expenseDiv);
    });
}

function getExpenseIcon(category) {
    const icons = {
        'food': 'utensils',
        'transportation': 'car',
        'entertainment': 'film',
        'shopping': 'shopping-bag',
        'freelance': 'briefcase',
        'default': 'receipt'
    };
    return icons[category] || icons.default;
}

// Charts
function initializeCharts() {
    if (!dashboardData) return;
    
    // Expense trend chart
    const expenseCtx = document.getElementById('expenseChart').getContext('2d');
    new Chart(expenseCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Expenses',
                data: [1200, 1900, 800, 1500, 2000, dashboardData.monthlyStats.expenses],
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4
            }, {
                label: 'Income',
                data: [2000, 2200, 1800, 2100, 2400, dashboardData.monthlyStats.income],
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Category breakdown chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    const categoryData = dashboardData.categoryBreakdown;
    
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: categoryData.map(cat => cat._id.charAt(0).toUpperCase() + cat._id.slice(1)),
            datasets: [{
                data: categoryData.map(cat => cat.total),
                backgroundColor: [
                    '#ef4444', '#3b82f6', '#8b5cf6', '#f59e0b', '#10b981',
                    '#ec4899', '#6366f1', '#84cc16', '#f97316', '#06b6d4'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Expenses
async function loadExpenses() {
    try {
        const response = await fetch('/api/expenses');
        const data = await response.json();
        expenses = data.expenses;
        
        displayExpenses();
    } catch (error) {
        console.error('Load expenses error:', error);
        showNotification('Failed to load expenses', 'error');
    }
}

function displayExpenses() {
    const expensesList = document.getElementById('expensesList');
    expensesList.innerHTML = '';
    
    expenses.forEach(expense => {
        const expenseDiv = document.createElement('div');
        expenseDiv.className = 'bg-white rounded-lg shadow-sm p-6';
        expenseDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-${expense.type === 'income' ? 'green' : 'red'}-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-${getExpenseIcon(expense.category)} text-${expense.type === 'income' ? 'green' : 'red'}-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold">${expense.title}</h3>
                        <p class="text-gray-600">${expense.description || ''}</p>
                        <p class="text-sm text-gray-500">${expense.category} • ${expense.date}</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-${expense.type === 'income' ? 'green' : 'red'}-600">
                        ${expense.type === 'income' ? '+' : '-'}$${expense.amount.toLocaleString()}
                    </p>
                </div>
            </div>
        `;
        expensesList.appendChild(expenseDiv);
    });
}

// AI Features
function showBillScanner() {
    document.getElementById('billScannerModal').classList.remove('hidden');
}

function closeBillScanner() {
    document.getElementById('billScannerModal').classList.add('hidden');
}

function showVoiceInput() {
    document.getElementById('voiceInputModal').classList.remove('hidden');
}

function closeVoiceInput() {
    document.getElementById('voiceInputModal').classList.add('hidden');
}

async function simulateBillScan() {
    try {
        showNotification('Analyzing bill with AI...', 'info');
        
        const response = await fetch('/api/ai/analyze-bill', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            closeBillScanner();
            showAIResults(data.data);
            showNotification(`Bill analyzed with ${data.data.confidence}% confidence!`, 'success');
        }
    } catch (error) {
        console.error('Bill scan error:', error);
        showNotification('Failed to analyze bill', 'error');
    }
}

async function simulateVoiceInput() {
    const voiceText = document.getElementById('voiceTextInput').value || "I spent 500 on milk yesterday";
    
    try {
        showNotification('Processing voice input with AI...', 'info');
        
        const response = await fetch('/api/ai/process-voice', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ voiceText })
        });
        
        const data = await response.json();
        
        if (data.success) {
            closeVoiceInput();
            showAIResults(data.analysisData);
            showNotification(`Voice input processed with ${data.analysisData.confidence}% confidence!`, 'success');
        }
    } catch (error) {
        console.error('Voice input error:', error);
        showNotification('Failed to process voice input', 'error');
    }
}

function showAIResults(data) {
    document.getElementById('aiTitle').value = data.title;
    document.getElementById('aiAmount').value = data.amount;
    document.getElementById('aiCategory').value = data.category;
    document.getElementById('aiDate').value = data.date;
    
    document.getElementById('aiResults').classList.remove('hidden');
    
    // Scroll to results
    document.getElementById('aiResults').scrollIntoView({ behavior: 'smooth' });
}

async function saveAIExpense() {
    const expenseData = {
        title: document.getElementById('aiTitle').value,
        amount: parseFloat(document.getElementById('aiAmount').value),
        category: document.getElementById('aiCategory').value,
        date: document.getElementById('aiDate').value,
        type: 'expense',
        description: 'Added via AI'
    };
    
    try {
        const response = await fetch('/api/expenses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(expenseData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('AI expense saved successfully!', 'success');
            document.getElementById('aiResults').classList.add('hidden');
            loadExpenses();
            loadDashboard();
        }
    } catch (error) {
        console.error('Save expense error:', error);
        showNotification('Failed to save expense', 'error');
    }
}

// Groups
async function loadGroups() {
    try {
        const response = await fetch('/api/groups');
        const data = await response.json();
        
        displayGroups(data.groups);
    } catch (error) {
        console.error('Load groups error:', error);
        showNotification('Failed to load groups', 'error');
    }
}

function displayGroups(groups) {
    const groupsList = document.getElementById('groupsList');
    groupsList.innerHTML = '';
    
    groups.forEach(group => {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'bg-white rounded-lg shadow-sm p-6 card-hover';
        groupDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">${group.name}</h3>
                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">Active</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">${group.members.length} members</p>
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span>Total Expenses:</span>
                    <span class="font-medium">$${group.totalExpenses.toLocaleString()}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span>Description:</span>
                    <span class="font-medium">${group.description}</span>
                </div>
            </div>
        `;
        groupsList.appendChild(groupDiv);
    });
}

// Utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add expense modal functions
function showAddExpense() {
    showNotification('Manual expense form would open here', 'info');
}

console.log('✅ Splitify JavaScript loaded successfully!');
