import React, { useState, useEffect } from 'react';
import { usersAPI, expensesAPI } from '../../utils/api';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { <PERSON><PERSON><PERSON>, <PERSON>hnut<PERSON>hart, LineChart } from '../../components/Charts/ExpenseChart';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  CreditCard,
  Users,
  Receipt
} from 'lucide-react';
import { format, subMonths } from 'date-fns';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [expenseSummary, setExpenseSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month');

  useEffect(() => {
    loadDashboardData();
    loadExpenseSummary();
  }, [period]);

  const loadDashboardData = async () => {
    try {
      const response = await usersAPI.getDashboard();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const loadExpenseSummary = async () => {
    try {
      const response = await expensesAPI.getSummary({ period });
      setExpenseSummary(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Failed to load expense summary:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const stats = [
    {
      name: 'Total Income',
      value: `$${dashboardData?.user?.totalIncome?.toLocaleString() || '0'}`,
      icon: TrendingUp,
      color: 'text-success-600',
      bgColor: 'bg-success-50',
    },
    {
      name: 'Total Expenses',
      value: `$${dashboardData?.user?.totalExpenses?.toLocaleString() || '0'}`,
      icon: TrendingDown,
      color: 'text-danger-600',
      bgColor: 'bg-danger-50',
    },
    {
      name: 'Monthly Balance',
      value: `$${dashboardData?.monthlyStats?.balance?.toLocaleString() || '0'}`,
      icon: DollarSign,
      color: dashboardData?.monthlyStats?.balance >= 0 ? 'text-success-600' : 'text-danger-600',
      bgColor: dashboardData?.monthlyStats?.balance >= 0 ? 'bg-success-50' : 'bg-danger-50',
    },
    {
      name: 'Active Groups',
      value: dashboardData?.user?.groups?.length || '0',
      icon: Users,
      color: 'text-primary-600',
      bgColor: 'bg-primary-50',
    },
  ];

  // Prepare chart data
  const categoryChartData = {
    labels: dashboardData?.categoryBreakdown?.map(cat => 
      cat._id.charAt(0).toUpperCase() + cat._id.slice(1).replace('-', ' ')
    ) || [],
    datasets: [
      {
        data: dashboardData?.categoryBreakdown?.map(cat => cat.total) || [],
        backgroundColor: [
          '#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4',
          '#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'
        ],
        borderWidth: 2,
        borderColor: '#ffffff',
      },
    ],
  };

  // Mock trend data (in real app, this would come from API)
  const trendData = {
    labels: Array.from({ length: 6 }, (_, i) => 
      format(subMonths(new Date(), 5 - i), 'MMM yyyy')
    ),
    datasets: [
      {
        label: 'Income',
        data: [3200, 3400, 3100, 3600, 3800, dashboardData?.monthlyStats?.income || 0],
        borderColor: '#22c55e',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Expenses',
        data: [2800, 3100, 2900, 3200, 3400, dashboardData?.monthlyStats?.expenses || 0],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const monthlyComparisonData = {
    labels: ['Income', 'Expenses'],
    datasets: [
      {
        label: 'This Month',
        data: [
          dashboardData?.monthlyStats?.income || 0,
          dashboardData?.monthlyStats?.expenses || 0
        ],
        backgroundColor: ['#22c55e', '#ef4444'],
        borderWidth: 2,
        borderColor: '#ffffff',
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex items-center space-x-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="input w-auto"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="stat-card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Comparison */}
        <div className="chart-container">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Monthly Income vs Expenses
          </h3>
          <BarChart 
            data={monthlyComparisonData}
            height={300}
          />
        </div>

        {/* Category Breakdown */}
        <div className="chart-container">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Expense Categories
          </h3>
          {dashboardData?.categoryBreakdown?.length > 0 ? (
            <DoughnutChart 
              data={categoryChartData}
              height={300}
            />
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <Receipt className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No expense data available</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Trend Chart */}
      <div className="chart-container">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          6-Month Trend
        </h3>
        <LineChart 
          data={trendData}
          height={400}
        />
      </div>

      {/* Recent Expenses */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Expenses</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {dashboardData?.recentExpenses?.length > 0 ? (
            dashboardData.recentExpenses.map((expense) => (
              <div key={expense._id} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${
                    expense.type === 'income' ? 'bg-success-50' : 'bg-danger-50'
                  }`}>
                    <CreditCard className={`h-5 w-5 ${
                      expense.type === 'income' ? 'text-success-600' : 'text-danger-600'
                    }`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">{expense.title}</p>
                    <p className="text-sm text-gray-500">
                      {expense.category.charAt(0).toUpperCase() + expense.category.slice(1).replace('-', ' ')} • 
                      {format(new Date(expense.date), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${
                    expense.type === 'income' ? 'text-success-600' : 'text-danger-600'
                  }`}>
                    {expense.type === 'income' ? '+' : '-'}${expense.amount.toLocaleString()}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center text-gray-500">
              <Receipt className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No recent expenses</p>
            </div>
          )}
        </div>
      </div>

      {/* Active Groups */}
      {dashboardData?.user?.groups?.length > 0 && (
        <div className="card">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Active Groups</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {dashboardData.user.groups.map((group) => (
              <div key={group._id} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-primary-50">
                    <Users className="h-5 w-5 text-primary-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">{group.name}</p>
                    <p className="text-sm text-gray-500">
                      {group.members?.length || 0} members
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${group.totalExpenses?.toLocaleString() || '0'}
                  </p>
                  <p className="text-sm text-gray-500">Total expenses</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
