// Splitify Frontend JavaScript - Complete Application
let currentUser = null;
let expenses = [];
let dashboardData = null;
let charts = {};

// API Base URL
const API_BASE = window.location.origin + '/api';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Splitify Full Stack App Initialized');
    console.log('🔗 API Base:', API_BASE);
    
    // Check if user is logged in
    const token = localStorage.getItem('splitify_token');
    if (token) {
        showMainApp();
        loadDashboard();
    } else {
        showLoginModal();
    }
});

// Utility Functions
function showLoading() {
    document.getElementById('loadingOverlay').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loadingOverlay').classList.add('hidden');
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white shadow-lg transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' :
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Authentication Functions
function showLoginModal() {
    document.getElementById('loginModal').classList.remove('hidden');
    document.getElementById('mainApp').classList.add('hidden');
}

function showMainApp() {
    document.getElementById('loginModal').classList.add('hidden');
    document.getElementById('mainApp').classList.remove('hidden');
}

// Login form handler
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    showLoading();
    
    try {
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            localStorage.setItem('splitify_token', data.token);
            currentUser = data.user;
            document.getElementById('userName').textContent = data.user.name;
            showMainApp();
            await loadDashboard();
            showNotification('Welcome to Splitify! 🎉', 'success');
        } else {
            showNotification('Login failed: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please check your connection.', 'error');
    } finally {
        hideLoading();
    }
});

function logout() {
    localStorage.removeItem('splitify_token');
    currentUser = null;
    showLoginModal();
    showNotification('Logged out successfully', 'info');
}

// Navigation Functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.remove('hidden');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.className = 'nav-link text-gray-600 hover:text-blue-600 pb-2 transition-colors';
    });
    event.target.className = 'nav-link text-blue-600 border-b-2 border-blue-600 pb-2 font-medium';
    
    // Load section data
    if (sectionId === 'expenses') {
        loadExpenses();
    } else if (sectionId === 'groups') {
        loadGroups();
    } else if (sectionId === 'dashboard') {
        loadDashboard();
    }
}

// Dashboard Functions
async function loadDashboard() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/dashboard`);
        dashboardData = await response.json();
        
        updateDashboardStats();
        initializeCharts();
        updateRecentExpenses();
        
        showNotification('Dashboard updated', 'success');
    } catch (error) {
        console.error('Dashboard load error:', error);
        showNotification('Failed to load dashboard', 'error');
    } finally {
        hideLoading();
    }
}

function updateDashboardStats() {
    if (!dashboardData) return;
    
    const { monthlyStats, groups } = dashboardData;
    
    document.getElementById('totalIncome').textContent = '$' + monthlyStats.income.toLocaleString();
    document.getElementById('totalExpenses').textContent = '$' + monthlyStats.expenses.toLocaleString();
    
    const balance = monthlyStats.balance;
    const balanceElement = document.getElementById('balance');
    balanceElement.textContent = '$' + Math.abs(balance).toLocaleString();
    balanceElement.className = `text-2xl font-bold ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`;
    
    document.getElementById('activeGroups').textContent = groups.length;
}

function updateRecentExpenses() {
    if (!dashboardData) return;
    
    const recentList = document.getElementById('recentExpensesList');
    recentList.innerHTML = '';
    
    if (dashboardData.recentExpenses.length === 0) {
        recentList.innerHTML = `
            <div class="px-6 py-8 text-center text-gray-500">
                <i class="fas fa-receipt text-4xl mb-4"></i>
                <p>No expenses yet. Start by adding your first expense!</p>
            </div>
        `;
        return;
    }
    
    dashboardData.recentExpenses.forEach(expense => {
        const expenseDiv = document.createElement('div');
        expenseDiv.className = 'px-6 py-4 hover:bg-gray-50 transition-colors';
        expenseDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-${expense.type === 'income' ? 'green' : 'red'}-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-${getExpenseIcon(expense.category)} text-${expense.type === 'income' ? 'green' : 'red'}-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="font-medium text-gray-900">${expense.title}</p>
                        <p class="text-sm text-gray-500">${formatCategory(expense.category)} • ${formatDate(expense.date)}</p>
                        ${expense.description ? `<p class="text-xs text-gray-400">${expense.description}</p>` : ''}
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-medium text-${expense.type === 'income' ? 'green' : 'red'}-600">
                        ${expense.type === 'income' ? '+' : '-'}$${expense.amount.toLocaleString()}
                    </p>
                    <p class="text-xs text-gray-500">${expense.paymentMethod || 'N/A'}</p>
                </div>
            </div>
        `;
        recentList.appendChild(expenseDiv);
    });
}

function getExpenseIcon(category) {
    const icons = {
        'food': 'utensils',
        'transportation': 'car',
        'entertainment': 'film',
        'shopping': 'shopping-bag',
        'freelance': 'briefcase',
        'utilities': 'bolt',
        'healthcare': 'heartbeat',
        'education': 'graduation-cap',
        'travel': 'plane',
        'default': 'receipt'
    };
    return icons[category] || icons.default;
}

function formatCategory(category) {
    return category.split('-').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
        return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
    } else {
        return date.toLocaleDateString();
    }
}

// Charts Functions
function initializeCharts() {
    if (!dashboardData) return;
    
    // Destroy existing charts
    Object.values(charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    
    // Monthly Trends Chart
    const expenseCtx = document.getElementById('expenseChart').getContext('2d');
    charts.expense = new Chart(expenseCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Expenses',
                data: [1200, 1900, 800, 1500, 2000, dashboardData.monthlyStats.expenses],
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Income',
                data: [2000, 2200, 1800, 2100, 2400, dashboardData.monthlyStats.income],
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Category Breakdown Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    const categoryData = dashboardData.categoryBreakdown;
    
    if (categoryData.length > 0) {
        charts.category = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: categoryData.map(cat => formatCategory(cat._id)),
                datasets: [{
                    data: categoryData.map(cat => cat.total),
                    backgroundColor: [
                        '#ef4444', '#3b82f6', '#8b5cf6', '#f59e0b', '#10b981',
                        '#ec4899', '#6366f1', '#84cc16', '#f97316', '#06b6d4'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = '$' + context.parsed.toLocaleString();
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    } else {
        // Show empty state
        categoryCtx.canvas.parentNode.innerHTML = `
            <div class="flex items-center justify-center h-64 text-gray-500">
                <div class="text-center">
                    <i class="fas fa-chart-pie text-4xl mb-2"></i>
                    <p>No expense data yet</p>
                </div>
            </div>
        `;
    }
}

// Expenses Functions
async function loadExpenses() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/expenses`);
        const data = await response.json();
        expenses = data.expenses;

        displayExpenses();
        showNotification('Expenses loaded', 'success');
    } catch (error) {
        console.error('Load expenses error:', error);
        showNotification('Failed to load expenses', 'error');
    } finally {
        hideLoading();
    }
}

function displayExpenses() {
    const expensesList = document.getElementById('expensesList');
    expensesList.innerHTML = '';

    if (expenses.length === 0) {
        expensesList.innerHTML = `
            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                <i class="fas fa-receipt text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No expenses yet</h3>
                <p class="text-gray-500 mb-6">Start tracking your expenses by adding your first entry</p>
                <div class="flex justify-center space-x-3">
                    <button onclick="showVoiceInput()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-microphone mr-2"></i>Voice Input
                    </button>
                    <button onclick="showBillScanner()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-camera mr-2"></i>Scan Bill
                    </button>
                </div>
            </div>
        `;
        return;
    }

    expenses.forEach(expense => {
        const expenseDiv = document.createElement('div');
        expenseDiv.className = 'bg-white rounded-lg shadow-sm p-6 card-hover';
        expenseDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-${expense.type === 'income' ? 'green' : 'red'}-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-${getExpenseIcon(expense.category)} text-${expense.type === 'income' ? 'green' : 'red'}-600 text-xl"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-lg font-semibold text-gray-900">${expense.title}</h3>
                            <span class="text-2xl font-bold text-${expense.type === 'income' ? 'green' : 'red'}-600">
                                ${expense.type === 'income' ? '+' : '-'}$${expense.amount.toLocaleString()}
                            </span>
                        </div>
                        <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                            <span>${formatCategory(expense.category)}</span>
                            <span>•</span>
                            <span>${expense.paymentMethod ? formatCategory(expense.paymentMethod) : 'N/A'}</span>
                            <span>•</span>
                            <span>${formatDate(expense.date)}</span>
                        </div>
                        ${expense.description ? `<p class="mt-2 text-sm text-gray-600">${expense.description}</p>` : ''}
                        ${expense.tags && expense.tags.length > 0 ? `
                            <div class="mt-2 flex flex-wrap gap-1">
                                ${expense.tags.map(tag => `
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        ${tag}
                                    </span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="editExpense('${expense.id}')" class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="Edit expense">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteExpense('${expense.id}')" class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Delete expense">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        expensesList.appendChild(expenseDiv);
    });
}

// AI Features Functions
function showBillScanner() {
    document.getElementById('billScannerModal').classList.remove('hidden');
    // Reset progress
    document.getElementById('billProgress').style.width = '0%';
}

function closeBillScanner() {
    document.getElementById('billScannerModal').classList.add('hidden');
}

function showVoiceInput() {
    document.getElementById('voiceInputModal').classList.remove('hidden');
}

function closeVoiceInput() {
    document.getElementById('voiceInputModal').classList.add('hidden');
}

async function simulateBillScan() {
    try {
        showNotification('🤖 AI analyzing bill...', 'info');

        // Animate progress bar
        const progressBar = document.getElementById('billProgress');
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;
            progressBar.style.width = progress + '%';
            if (progress >= 100) {
                clearInterval(interval);
            }
        }, 100);

        const response = await fetch(`${API_BASE}/ai/analyze-bill`, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            closeBillScanner();
            showAIResults(data.data);
            showNotification(`✅ Bill analyzed with ${data.data.confidence}% confidence!`, 'success');
        }
    } catch (error) {
        console.error('Bill scan error:', error);
        showNotification('Failed to analyze bill', 'error');
    }
}

async function simulateVoiceInput() {
    const voiceText = document.getElementById('voiceTextInput').value || "I spent 500 on milk yesterday";

    try {
        showNotification('🎤 Processing voice input with AI...', 'info');

        const response = await fetch(`${API_BASE}/ai/process-voice`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ voiceText })
        });

        const data = await response.json();

        if (data.success) {
            closeVoiceInput();
            showAIResults(data.analysisData);
            showNotification(`✅ Voice processed with ${data.analysisData.confidence}% confidence!`, 'success');
        }
    } catch (error) {
        console.error('Voice input error:', error);
        showNotification('Failed to process voice input', 'error');
    }
}

function showAIResults(data) {
    document.getElementById('aiTitle').value = data.title;
    document.getElementById('aiAmount').value = data.amount;
    document.getElementById('aiCategory').value = data.category;
    document.getElementById('aiDate').value = data.date;
    document.getElementById('aiDescription').value = data.description || '';
    document.getElementById('aiConfidence').textContent = `Confidence: ${data.confidence}%`;

    document.getElementById('aiResults').classList.remove('hidden');

    // Scroll to results
    document.getElementById('aiResults').scrollIntoView({ behavior: 'smooth' });

    // Switch to AI features section if not already there
    showSection('ai-features');
}

async function saveAIExpense() {
    const expenseData = {
        title: document.getElementById('aiTitle').value,
        amount: parseFloat(document.getElementById('aiAmount').value),
        category: document.getElementById('aiCategory').value,
        date: document.getElementById('aiDate').value,
        type: 'expense',
        description: document.getElementById('aiDescription').value,
        paymentMethod: 'other',
        tags: ['AI-generated']
    };

    try {
        showLoading();
        const response = await fetch(`${API_BASE}/expenses`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(expenseData)
        });

        const data = await response.json();

        if (data.success) {
            showNotification('✅ AI expense saved successfully!', 'success');
            document.getElementById('aiResults').classList.add('hidden');

            // Refresh data
            await loadExpenses();
            await loadDashboard();
        }
    } catch (error) {
        console.error('Save expense error:', error);
        showNotification('Failed to save expense', 'error');
    } finally {
        hideLoading();
    }
}

// Groups Functions
async function loadGroups() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/groups`);
        const data = await response.json();

        displayGroups(data.groups);
        showNotification('Groups loaded', 'success');
    } catch (error) {
        console.error('Load groups error:', error);
        showNotification('Failed to load groups', 'error');
    } finally {
        hideLoading();
    }
}

function displayGroups(groups) {
    const groupsList = document.getElementById('groupsList');
    groupsList.innerHTML = '';

    if (groups.length === 0) {
        groupsList.innerHTML = `
            <div class="col-span-3 bg-white rounded-lg shadow-sm p-12 text-center">
                <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No groups yet</h3>
                <p class="text-gray-500 mb-6">Create your first group to start splitting expenses</p>
                <button class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>Create Group
                </button>
            </div>
        `;
        return;
    }

    groups.forEach(group => {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'bg-white rounded-lg shadow-sm p-6 card-hover cursor-pointer';
        groupDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-lg">${group.name}</h3>
                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">Active</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">${group.description}</p>
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">Members:</span>
                    <span class="font-medium">${group.members.length}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">Total Expenses:</span>
                    <span class="font-medium">$${group.totalExpenses.toLocaleString()}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-500">Currency:</span>
                    <span class="font-medium">${group.currency}</span>
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex items-center space-x-2">
                    <div class="flex -space-x-2">
                        ${group.members.slice(0, 3).map((member, index) => `
                            <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-medium border-2 border-white" title="${member}">
                                ${member.charAt(0).toUpperCase()}
                            </div>
                        `).join('')}
                        ${group.members.length > 3 ? `
                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 text-sm font-medium border-2 border-white">
                                +${group.members.length - 3}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        groupsList.appendChild(groupDiv);
    });
}

// Additional Functions
function showAddExpense() {
    showNotification('Manual expense form would open here. Try AI features instead!', 'info');
}

function editExpense(expenseId) {
    showNotification('Edit functionality would open here', 'info');
}

function deleteExpense(expenseId) {
    if (confirm('Are you sure you want to delete this expense?')) {
        showNotification('Delete functionality would be implemented here', 'info');
    }
}

// Initialize tooltips and other UI enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});

console.log('✅ Splitify Full Stack Application Loaded Successfully!');
console.log('🎯 Features available:');
console.log('   • User Authentication');
console.log('   • Interactive Dashboard with Charts');
console.log('   • Expense Management');
console.log('   • AI Bill Scanner');
console.log('   • AI Voice Input');
console.log('   • Group Management');
console.log('🚀 Ready for full testing!');
