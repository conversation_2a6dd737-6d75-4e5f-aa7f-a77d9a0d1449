@echo off
echo ========================================
echo    SPLITIFY - AI-Powered Expense App
echo ========================================
echo.

echo [1/5] Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error installing root dependencies
    pause
    exit /b 1
)

echo.
echo [2/5] Installing server dependencies...
cd server
call npm install
if %errorlevel% neq 0 (
    echo Error installing server dependencies
    pause
    exit /b 1
)

echo.
echo [3/5] Installing client dependencies...
cd ..\client
call npm install
if %errorlevel% neq 0 (
    echo Error installing client dependencies
    pause
    exit /b 1
)

echo.
echo [4/5] Starting MongoDB (if available)...
echo Note: Make sure MongoDB is running on your system
echo You can start it with: mongod --dbpath "C:\data\db"
echo.

echo [5/5] Starting the application...
cd ..
echo.
echo ========================================
echo  Starting Splitify Application
echo ========================================
echo  Backend API: http://localhost:5000
echo  Frontend:    http://localhost:3000
echo ========================================
echo.

call npm run dev

pause
