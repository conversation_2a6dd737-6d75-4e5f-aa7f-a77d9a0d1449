Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   SPLITIFY - AI-Powered Expense App" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    Write-Host "Download from: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "✓ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm is not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[1/5] Installing root dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Error installing root dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[2/5] Installing server dependencies..." -ForegroundColor Yellow
Set-Location server
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Error installing server dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[3/5] Installing client dependencies..." -ForegroundColor Yellow
Set-Location ..\client
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Error installing client dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[4/5] Checking MongoDB..." -ForegroundColor Yellow
Write-Host "Note: Make sure MongoDB is running on your system" -ForegroundColor Cyan
Write-Host "You can start it with: mongod --dbpath `"C:\data\db`"" -ForegroundColor Cyan
Write-Host ""

Write-Host "[5/5] Starting the application..." -ForegroundColor Yellow
Set-Location ..

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host " Starting Splitify Application" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host " Backend API: http://localhost:5000" -ForegroundColor Cyan
Write-Host " Frontend:    http://localhost:3000" -ForegroundColor Cyan
Write-Host " AI Features: Gemini API Integrated" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Start the development servers
npm run dev
