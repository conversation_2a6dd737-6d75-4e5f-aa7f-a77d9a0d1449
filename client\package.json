{"name": "splitify-client", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "socket.io-client": "^4.7.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.279.0", "date-fns": "^2.30.0", "react-hook-form": "^7.45.4", "react-select": "^5.7.4", "react-datepicker": "^4.16.0", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-datepicker": "^4.15.0"}, "proxy": "http://localhost:5000"}