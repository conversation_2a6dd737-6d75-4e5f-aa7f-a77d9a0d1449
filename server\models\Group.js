const mongoose = require('mongoose');

const groupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    role: {
      type: String,
      enum: ['admin', 'member'],
      default: 'member'
    }
  }],
  expenses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'GroupExpense'
  }],
  totalExpenses: {
    type: Number,
    default: 0
  },
  currency: {
    type: String,
    default: 'USD'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  settings: {
    allowMemberAddExpenses: {
      type: Boolean,
      default: true
    },
    requireApproval: {
      type: Boolean,
      default: false
    },
    splitMethod: {
      type: String,
      enum: ['equal', 'percentage', 'custom'],
      default: 'equal'
    }
  }
}, {
  timestamps: true
});

// Method to calculate balances for all members
groupSchema.methods.calculateBalances = async function() {
  const GroupExpense = mongoose.model('GroupExpense');
  
  const expenses = await GroupExpense.find({ group: this._id }).populate('paidBy');
  const balances = {};
  
  // Initialize balances for all members
  this.members.forEach(member => {
    balances[member.user.toString()] = 0;
  });
  
  // Calculate what each member owes/is owed
  expenses.forEach(expense => {
    const paidById = expense.paidBy._id.toString();
    
    // Add the amount paid to the payer's balance
    balances[paidById] += expense.amount;
    
    // Subtract each member's share from their balance
    expense.splits.forEach(split => {
      const userId = split.user.toString();
      balances[userId] -= split.amount;
    });
  });
  
  return balances;
};

// Method to get settlement suggestions
groupSchema.methods.getSettlements = async function() {
  const balances = await this.calculateBalances();
  const settlements = [];
  
  const creditors = []; // People who are owed money
  const debtors = [];   // People who owe money
  
  Object.entries(balances).forEach(([userId, balance]) => {
    if (balance > 0.01) { // Creditor (owed money)
      creditors.push({ userId, amount: balance });
    } else if (balance < -0.01) { // Debtor (owes money)
      debtors.push({ userId, amount: Math.abs(balance) });
    }
  });
  
  // Sort by amount (largest first)
  creditors.sort((a, b) => b.amount - a.amount);
  debtors.sort((a, b) => b.amount - a.amount);
  
  // Calculate optimal settlements
  let i = 0, j = 0;
  while (i < creditors.length && j < debtors.length) {
    const creditor = creditors[i];
    const debtor = debtors[j];
    
    const settleAmount = Math.min(creditor.amount, debtor.amount);
    
    settlements.push({
      from: debtor.userId,
      to: creditor.userId,
      amount: settleAmount
    });
    
    creditor.amount -= settleAmount;
    debtor.amount -= settleAmount;
    
    if (creditor.amount === 0) i++;
    if (debtor.amount === 0) j++;
  }
  
  return settlements;
};

module.exports = mongoose.model('Group', groupSchema);
