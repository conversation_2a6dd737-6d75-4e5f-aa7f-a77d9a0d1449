{"name": "splitify-server", "version": "1.0.0", "description": "Backend server for Splitify expense tracking application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4"}, "keywords": ["express", "mongodb", "api", "expense", "tracking"], "author": "Your Name", "license": "MIT"}