<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splitify - AI-Powered Expense Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .hidden { display: none; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h2 class="text-2xl font-bold text-center mb-6">Login to Splitify</h2>
                <form id="loginForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="email" value="<EMAIL>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" id="password" value="password123"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                        Login
                    </button>
                </form>
                <p class="text-center text-sm text-gray-600 mt-4">
                    Demo credentials are pre-filled
                </p>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="hidden">
        <!-- Header -->
        <header class="gradient-bg text-white py-6">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-chart-pie text-3xl"></i>
                        <h1 class="text-3xl font-bold">Splitify</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="userName" class="text-lg"></span>
                        <button onclick="logout()" class="bg-white bg-opacity-20 px-3 py-1 rounded-full hover:bg-opacity-30">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            Logout
                        </button>
                    </div>
                </div>
                <p class="mt-2 text-lg opacity-90">Smart Expense Tracking with AI Integration</p>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b">
            <div class="container mx-auto px-4">
                <div class="flex space-x-8 py-4">
                    <a href="#" onclick="showSection('dashboard')" class="nav-link text-blue-600 border-b-2 border-blue-600 pb-2 font-medium">Dashboard</a>
                    <a href="#" onclick="showSection('expenses')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">Expenses</a>
                    <a href="#" onclick="showSection('ai-features')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">AI Features</a>
                    <a href="#" onclick="showSection('groups')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">Groups</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="container mx-auto px-4 py-8">
            <!-- Dashboard Section -->
            <section id="dashboard" class="section">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Dashboard Overview</h2>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-lg">
                                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Income</p>
                                <p id="totalIncome" class="text-2xl font-bold text-gray-900">$0</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="flex items-center">
                            <div class="p-3 bg-red-100 rounded-lg">
                                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Expenses</p>
                                <p id="totalExpenses" class="text-2xl font-bold text-gray-900">$0</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="flex items-center">
                            <div class="p-3 bg-blue-100 rounded-lg">
                                <i class="fas fa-wallet text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Balance</p>
                                <p id="balance" class="text-2xl font-bold text-green-600">$0</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="flex items-center">
                            <div class="p-3 bg-purple-100 rounded-lg">
                                <i class="fas fa-users text-purple-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Active Groups</p>
                                <p id="activeGroups" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Monthly Expenses</h3>
                        <canvas id="expenseChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Category Breakdown</h3>
                        <canvas id="categoryChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Recent Expenses -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold">Recent Expenses</h3>
                    </div>
                    <div id="recentExpensesList" class="divide-y divide-gray-200">
                        <!-- Recent expenses will be loaded here -->
                    </div>
                </div>
            </section>

            <!-- Expenses Section -->
            <section id="expenses" class="section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Expenses</h2>
                    <div class="flex space-x-3">
                        <button onclick="showVoiceInput()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                            <i class="fas fa-microphone mr-2"></i>
                            Voice Input
                        </button>
                        <button onclick="showBillScanner()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            <i class="fas fa-camera mr-2"></i>
                            Scan Bill
                        </button>
                        <button onclick="showAddExpense()" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                            <i class="fas fa-plus mr-2"></i>
                            Add Manual
                        </button>
                    </div>
                </div>

                <div id="expensesList" class="space-y-4">
                    <!-- Expenses will be loaded here -->
                </div>
            </section>

            <!-- AI Features Section -->
            <section id="ai-features" class="section hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">
                    <i class="fas fa-robot mr-2 text-blue-600"></i>
                    AI-Powered Features
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Bill Scanner -->
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-camera text-blue-600 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Smart Bill Scanner</h3>
                            <p class="text-gray-600 mb-4">Upload or take a photo of any receipt. AI extracts amount, date, merchant, and category automatically.</p>
                            
                            <button onclick="showBillScanner()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-camera mr-2"></i>
                                Try Bill Scanner
                            </button>
                            
                            <div class="mt-4 text-sm text-gray-500">
                                <p><strong>Confidence:</strong> 95% accuracy</p>
                                <p><strong>Supports:</strong> Receipts, bills, invoices</p>
                            </div>
                        </div>
                    </div>

                    <!-- Voice Input -->
                    <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-microphone text-green-600 text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Voice Expense Input</h3>
                            <p class="text-gray-600 mb-4">Simply speak your expense details. "I spent 500 on milk yesterday" - AI understands natural language.</p>
                            
                            <button onclick="showVoiceInput()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-microphone mr-2"></i>
                                Try Voice Input
                            </button>
                            
                            <div class="mt-4 text-sm text-gray-500">
                                <p><strong>Examples:</strong> Natural speech</p>
                                <p><strong>Accuracy:</strong> 92% recognition</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Results -->
                <div id="aiResults" class="mt-8 hidden">
                    <h3 class="text-lg font-semibold mb-4">AI Extraction Results</h3>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Title</label>
                                <input type="text" id="aiTitle" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Amount</label>
                                <input type="number" id="aiAmount" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Category</label>
                                <select id="aiCategory" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="food">Food</option>
                                    <option value="transportation">Transportation</option>
                                    <option value="entertainment">Entertainment</option>
                                    <option value="shopping">Shopping</option>
                                    <option value="other-expense">Other</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Date</label>
                                <input type="date" id="aiDate" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>
                        <div class="mt-4">
                            <button onclick="saveAIExpense()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-2"></i>
                                Save Expense
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Groups Section -->
            <section id="groups" class="section hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Expense Groups</h2>
                
                <div id="groupsList" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Groups will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <!-- Bill Scanner Modal -->
    <div id="billScannerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">AI Bill Scanner</h3>
                    <button onclick="closeBillScanner()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-center py-8">
                    <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-animation">
                        <i class="fas fa-camera text-blue-600 text-3xl"></i>
                    </div>
                    <p class="text-gray-600 mb-4">AI is analyzing your bill...</p>
                    <div class="bg-gray-200 rounded-full h-2 mb-4">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                    </div>
                    <button onclick="simulateBillScan()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                        Simulate Bill Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Input Modal -->
    <div id="voiceInputModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Voice Expense Input</h3>
                    <button onclick="closeVoiceInput()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-center py-8">
                    <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-animation">
                        <i class="fas fa-microphone text-green-600 text-3xl"></i>
                    </div>
                    <p class="text-gray-600 mb-4">Listening... Speak your expense details</p>
                    <p class="text-sm text-gray-500 mb-4">Try: "I spent 500 on milk yesterday"</p>
                    <input type="text" id="voiceTextInput" placeholder="Or type your expense here..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md mb-4">
                    <button onclick="simulateVoiceInput()" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">
                        Process Voice Input
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
