@echo off
title Splitify - AI-Powered Expense Tracker Setup

echo.
echo ========================================
echo    SPLITIFY - FULL STACK SETUP
echo ========================================
echo.
echo This script will:
echo 1. Install all dependencies
echo 2. Set up sample data
echo 3. Start the full application
echo.
echo Requirements:
echo - Node.js (v14 or higher)
echo - MongoDB (running locally or Atlas)
echo.
pause

echo.
echo [STEP 1/6] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js is installed

echo.
echo [STEP 2/6] Installing root dependencies...
call npm install concurrently
if %errorlevel% neq 0 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)
echo ✅ Root dependencies installed

echo.
echo [STEP 3/6] Installing server dependencies...
cd server
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install server dependencies
    pause
    exit /b 1
)
echo ✅ Server dependencies installed

echo.
echo [STEP 4/6] Installing client dependencies...
cd ..\client
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install client dependencies
    pause
    exit /b 1
)
echo ✅ Client dependencies installed

echo.
echo [STEP 5/6] Setting up sample data...
cd ..\server
call npm run seed
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Could not create sample data (MongoDB might not be running)
    echo The app will still work, but you'll need to register a new account
) else (
    echo ✅ Sample data created
    echo.
    echo 📧 Demo Login: <EMAIL>
    echo 🔑 Password: password123
)

echo.
echo [STEP 6/6] Starting the application...
cd ..
echo.
echo ========================================
echo   🚀 STARTING SPLITIFY APPLICATION
echo ========================================
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:5000
echo 🤖 AI Features: Gemini API Integrated
echo.
echo ========================================
echo.

start "" "http://localhost:3000"
call npm run dev

echo.
echo Application stopped.
pause
