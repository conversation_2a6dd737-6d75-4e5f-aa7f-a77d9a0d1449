#!/usr/bin/env python3
import http.server
import socketserver
import json
import urllib.parse
from datetime import datetime
import os
import webbrowser
import threading
import time

# Sample data
sample_data = {
    "user": {
        "name": "Demo User",
        "email": "<EMAIL>",
        "totalIncome": 5000,
        "totalExpenses": 3200
    },
    "expenses": [
        {
            "id": "1",
            "title": "Grocery Shopping",
            "amount": 85.50,
            "category": "food",
            "date": "2024-01-15",
            "type": "expense",
            "description": "Weekly groceries from Walmart"
        },
        {
            "id": "2", 
            "title": "Gas Station",
            "amount": 45.00,
            "category": "transportation",
            "date": "2024-01-14",
            "type": "expense",
            "description": "Fuel for car"
        },
        {
            "id": "3",
            "title": "Freelance Payment",
            "amount": 1200.00,
            "category": "freelance",
            "date": "2024-01-13",
            "type": "income",
            "description": "Web development project"
        }
    ],
    "groups": [
        {
            "id": "1",
            "name": "Weekend Trip",
            "description": "Expenses for our weekend getaway",
            "totalExpenses": 450.00,
            "members": ["Demo User", "<PERSON> Doe", "<PERSON> <PERSON>"]
        }
    ]
}

class SplitifyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_main_page()
        elif self.path.startswith('/api/'):
            self.handle_api_get()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_post()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splitify - AI-Powered Expense Tracking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .hidden { display: none; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-chart-pie text-3xl"></i>
                    <h1 class="text-3xl font-bold">Splitify</h1>
                </div>
                <div class="text-sm">
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                        <i class="fas fa-robot mr-1"></i>
                        AI-Powered Demo
                    </span>
                </div>
            </div>
            <p class="mt-2 text-lg opacity-90">Smart Expense Tracking with AI Integration</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4">
            <div class="flex space-x-8 py-4">
                <a href="#" onclick="showSection('dashboard')" class="nav-link text-blue-600 border-b-2 border-blue-600 pb-2 font-medium">Dashboard</a>
                <a href="#" onclick="showSection('expenses')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">Expenses</a>
                <a href="#" onclick="showSection('ai-features')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">AI Features</a>
                <a href="#" onclick="showSection('groups')" class="nav-link text-gray-600 hover:text-blue-600 pb-2">Groups</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Dashboard Section -->
        <section id="dashboard" class="section">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Dashboard Overview</h2>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i class="fas fa-arrow-up text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Total Income</p>
                            <p class="text-2xl font-bold text-gray-900">$5,000</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-red-100 rounded-lg">
                            <i class="fas fa-arrow-down text-red-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Total Expenses</p>
                            <p class="text-2xl font-bold text-gray-900">$3,200</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i class="fas fa-wallet text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Balance</p>
                            <p class="text-2xl font-bold text-green-600">$1,800</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-lg">
                            <i class="fas fa-users text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Active Groups</p>
                            <p class="text-2xl font-bold text-gray-900">1</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Features Demo -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-camera text-blue-600 mr-2"></i>
                        Smart Bill Scanner
                    </h3>
                    <p class="text-gray-600 mb-4">Upload receipt photos and let AI extract all expense details automatically.</p>
                    <button onclick="simulateBillScan()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-camera mr-2"></i>
                        Try Bill Scanner
                    </button>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-microphone text-green-600 mr-2"></i>
                        Voice Expense Input
                    </h3>
                    <p class="text-gray-600 mb-4">Speak naturally: "I spent 500 on milk yesterday" and watch AI parse it.</p>
                    <button onclick="simulateVoiceInput()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-microphone mr-2"></i>
                        Try Voice Input
                    </button>
                </div>
            </div>

            <!-- Recent Expenses -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold">Recent Expenses</h3>
                </div>
                <div class="divide-y divide-gray-200">
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-utensils text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Grocery Shopping</p>
                                <p class="text-sm text-gray-500">Food • 2024-01-15 • AI Scanned</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-red-600">-$85.50</p>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-gas-pump text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Gas Station</p>
                                <p class="text-sm text-gray-500">Transportation • 2024-01-14 • Voice Input</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-red-600">-$45.00</p>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-briefcase text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium">Freelance Payment</p>
                                <p class="text-sm text-gray-500">Income • 2024-01-13 • Manual</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-green-600">+$1,200.00</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- AI Results -->
        <div id="aiResults" class="mt-8 hidden">
            <h3 class="text-lg font-semibold mb-4">AI Extraction Results</h3>
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Title</label>
                        <input type="text" id="aiTitle" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Amount</label>
                        <input type="text" id="aiAmount" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Category</label>
                        <input type="text" id="aiCategory" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="text" id="aiDate" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" readonly>
                    </div>
                </div>
                <div class="mt-4">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Expense
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        function showSection(sectionId) {
            // Simple navigation - for demo purposes
            console.log('Showing section:', sectionId);
        }

        function simulateBillScan() {
            showNotification('🤖 AI analyzing bill...', 'info');
            
            setTimeout(() => {
                document.getElementById('aiTitle').value = 'Grocery Store Receipt';
                document.getElementById('aiAmount').value = '$85.50';
                document.getElementById('aiCategory').value = 'Food';
                document.getElementById('aiDate').value = '2024-01-15';
                document.getElementById('aiResults').classList.remove('hidden');
                
                showNotification('✅ Bill analyzed with 95% confidence!', 'success');
                document.getElementById('aiResults').scrollIntoView({ behavior: 'smooth' });
            }, 2000);
        }

        function simulateVoiceInput() {
            showNotification('🎤 Processing voice: "I spent 500 on milk yesterday"', 'info');
            
            setTimeout(() => {
                document.getElementById('aiTitle').value = 'Milk Purchase';
                document.getElementById('aiAmount').value = '$500.00';
                document.getElementById('aiCategory').value = 'Food';
                document.getElementById('aiDate').value = '2024-01-14';
                document.getElementById('aiResults').classList.remove('hidden');
                
                showNotification('✅ Voice processed with 92% confidence!', 'success');
                document.getElementById('aiResults').scrollIntoView({ behavior: 'smooth' });
            }, 2000);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        console.log('🚀 Splitify Demo Loaded Successfully!');
        console.log('🤖 AI Features: Bill Scanner & Voice Input Ready');
        console.log('📊 Dashboard: Real-time expense tracking');
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def handle_api_get(self):
        if self.path == '/api/dashboard':
            self.send_json_response(sample_data)
        elif self.path == '/api/expenses':
            self.send_json_response({"expenses": sample_data["expenses"]})
        elif self.path == '/api/groups':
            self.send_json_response({"groups": sample_data["groups"]})
        else:
            self.send_error(404)
    
    def handle_api_post(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        if self.path == '/api/ai/analyze-bill':
            response = {
                "success": True,
                "message": "Bill analyzed successfully",
                "data": {
                    "title": "Grocery Store Receipt",
                    "amount": 85.50,
                    "date": "2024-01-15",
                    "category": "food",
                    "merchant": "Walmart",
                    "confidence": 95
                }
            }
            self.send_json_response(response)
        elif self.path == '/api/ai/process-voice':
            response = {
                "success": True,
                "message": "Voice processed successfully",
                "analysisData": {
                    "title": "Milk Purchase",
                    "amount": 500,
                    "date": "2024-01-14",
                    "category": "food",
                    "confidence": 92
                }
            }
            self.send_json_response(response)
        else:
            self.send_error(404)
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())

def open_browser():
    time.sleep(1)
    webbrowser.open('http://localhost:8000')

if __name__ == "__main__":
    PORT = 8000
    
    print("🚀 Starting Splitify Demo Server...")
    print("=" * 50)
    print(f"🌐 Frontend: http://localhost:{PORT}")
    print("🤖 AI Features: Simulated Gemini API")
    print("📊 Dashboard: Interactive charts and stats")
    print("=" * 50)
    print("✨ Features to try:")
    print("   • Smart Bill Scanner")
    print("   • Voice Expense Input")
    print("   • Interactive Dashboard")
    print("=" * 50)
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    with socketserver.TCPServer(("", PORT), SplitifyHandler) as httpd:
        print(f"🎯 Server running at http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Server stopped!")
