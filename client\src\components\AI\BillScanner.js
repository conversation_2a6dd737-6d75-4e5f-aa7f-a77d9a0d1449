import React, { useState, useRef } from 'react';
import { Camera, Upload, X, Loader, CheckCircle, AlertCircle } from 'lucide-react';
import { aiAPI } from '../../utils/api';
import LoadingSpinner from '../UI/LoadingSpinner';
import toast from 'react-hot-toast';

const BillScanner = ({ onExpenseExtracted, onClose }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [editableData, setEditableData] = useState(null);
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const handleFileSelect = (file) => {
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setAnalysisResult(null);
      setEditableData(null);
    } else {
      toast.error('Please select a valid image file');
    }
  };

  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const analyzeBill = async () => {
    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    setAnalyzing(true);
    try {
      const response = await aiAPI.analyzeBill(selectedFile);
      const result = response.data.data;
      
      setAnalysisResult(result);
      setEditableData({
        title: result.title,
        amount: result.amount,
        date: result.date,
        category: result.category,
        description: result.merchant ? `From ${result.merchant}` : '',
        paymentMethod: result.paymentMethod || 'other',
      });
      
      toast.success(`Bill analyzed with ${result.confidence}% confidence`);
    } catch (error) {
      toast.error('Failed to analyze bill. Please try again.');
      console.error('Bill analysis error:', error);
    } finally {
      setAnalyzing(false);
    }
  };

  const handleDataChange = (field, value) => {
    setEditableData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const createExpense = async () => {
    if (!editableData) return;

    try {
      const expenseData = {
        ...editableData,
        amount: parseFloat(editableData.amount),
        type: 'expense',
        tags: analysisResult?.merchant ? [analysisResult.merchant] : [],
      };

      onExpenseExtracted(expenseData);
      toast.success('Expense data extracted successfully!');
      onClose();
    } catch (error) {
      toast.error('Failed to create expense');
      console.error('Expense creation error:', error);
    }
  };

  const categories = [
    { value: 'food', label: 'Food' },
    { value: 'transportation', label: 'Transportation' },
    { value: 'housing', label: 'Housing' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'shopping', label: 'Shopping' },
    { value: 'education', label: 'Education' },
    { value: 'travel', label: 'Travel' },
    { value: 'other-expense', label: 'Other' },
  ];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Camera className="h-5 w-5 mr-2" />
                AI Bill Scanner
              </h3>
              <button
                type="button"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* File Upload Section */}
              {!selectedFile && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                  <div className="text-center">
                    <Camera className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      Upload Bill or Receipt
                    </h4>
                    <p className="text-gray-500 mb-4">
                      Take a photo or upload an image of your bill/receipt
                    </p>
                    
                    <div className="flex justify-center space-x-4">
                      <button
                        onClick={() => cameraInputRef.current?.click()}
                        className="btn-primary"
                      >
                        <Camera className="h-4 w-4 mr-2" />
                        Take Photo
                      </button>
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="btn-secondary"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Image
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Hidden file inputs */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInputChange}
                className="hidden"
              />
              <input
                ref={cameraInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                onChange={handleFileInputChange}
                className="hidden"
              />

              {/* Image Preview */}
              {previewUrl && (
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={previewUrl}
                      alt="Bill preview"
                      className="w-full h-64 object-contain bg-gray-100 rounded-lg"
                    />
                    <button
                      onClick={() => {
                        setSelectedFile(null);
                        setPreviewUrl(null);
                        setAnalysisResult(null);
                        setEditableData(null);
                        URL.revokeObjectURL(previewUrl);
                      }}
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>

                  {!analyzing && !analysisResult && (
                    <button
                      onClick={analyzeBill}
                      className="btn-primary w-full"
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Analyze Bill with AI
                    </button>
                  )}

                  {analyzing && (
                    <div className="flex items-center justify-center py-8">
                      <LoadingSpinner size="lg" className="mr-3" />
                      <span className="text-gray-600">Analyzing bill with AI...</span>
                    </div>
                  )}
                </div>
              )}

              {/* Analysis Results */}
              {analysisResult && editableData && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-sm">
                    {analysisResult.confidence >= 80 ? (
                      <CheckCircle className="h-5 w-5 text-success-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    )}
                    <span className="text-gray-600">
                      Analysis confidence: {analysisResult.confidence}%
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="label">Title</label>
                      <input
                        type="text"
                        className="input"
                        value={editableData.title}
                        onChange={(e) => handleDataChange('title', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Amount</label>
                      <input
                        type="number"
                        step="0.01"
                        className="input"
                        value={editableData.amount}
                        onChange={(e) => handleDataChange('amount', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Date</label>
                      <input
                        type="date"
                        className="input"
                        value={editableData.date}
                        onChange={(e) => handleDataChange('date', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="label">Category</label>
                      <select
                        className="input"
                        value={editableData.category}
                        onChange={(e) => handleDataChange('category', e.target.value)}
                      >
                        {categories.map(cat => (
                          <option key={cat.value} value={cat.value}>
                            {cat.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="label">Description</label>
                    <textarea
                      className="input"
                      rows={2}
                      value={editableData.description}
                      onChange={(e) => handleDataChange('description', e.target.value)}
                    />
                  </div>

                  {analysisResult.items && analysisResult.items.length > 0 && (
                    <div>
                      <label className="label">Detected Items</label>
                      <div className="flex flex-wrap gap-2">
                        {analysisResult.items.map((item, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                          >
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {editableData && (
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                onClick={createExpense}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                Create Expense
              </button>
              <button
                onClick={onClose}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BillScanner;
