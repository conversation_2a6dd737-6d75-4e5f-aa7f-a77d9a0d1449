# 🚀 Splitify - Complete Setup Instructions

## 📋 Prerequisites

1. **Node.js** (v14 or higher) - [Download here](https://nodejs.org/)
2. **MongoDB** - Either:
   - Local installation: [Download MongoDB](https://www.mongodb.com/try/download/community)
   - Or use MongoDB Atlas (cloud): [MongoDB Atlas](https://www.mongodb.com/atlas)

## 🛠️ Quick Setup (Automated)

### Option 1: Windows Batch Script
```bash
# Double-click or run in Command Prompt:
setup-and-run.bat
```

### Option 2: PowerShell Script
```powershell
# Right-click and "Run with PowerShell":
.\start-app.ps1
```

## 🔧 Manual Setup

### Step 1: Install Dependencies
```bash
# In the root directory (c:\Placements Project\splitify)
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### Step 2: Environment Configuration
```bash
# Copy the environment file
cd server
copy .env.example .env

# Edit .env file with your settings:
PORT=5000
MONGODB_URI=mongodb://localhost:27017/splitify
JWT_SECRET=your_jwt_secret_key_here
GEMINI_API_KEY=AIzaSyA7mzy9PXeMClojxqeuPGqU2vNAyXCj2dg
```

### Step 3: Start MongoDB
```bash
# If using local MongoDB:
mongod --dbpath "C:\data\db"

# Or use MongoDB Atlas connection string in .env
```

### Step 4: Seed Sample Data (Optional)
```bash
cd server
npm run seed
```

### Step 5: Start the Application
```bash
# From root directory:
npm run dev

# This starts both:
# - Backend server: http://localhost:5000
# - Frontend app: http://localhost:3000
```

## 🎯 Testing the Application

### 1. Access the Frontend
- Open browser: `http://localhost:3000`
- Register a new account or use demo credentials:
  - Email: `<EMAIL>`
  - Password: `password123`

### 2. Test AI Features

#### 📸 Bill Scanner
1. Go to "Expenses" page
2. Click "Scan Bill" button
3. Upload any receipt/bill image
4. Watch AI extract data automatically

#### 🎤 Voice Input
1. Go to "Expenses" page
2. Click "Voice Input" button
3. Say: "I spent 500 on milk yesterday"
4. See AI parse and categorize automatically

### 3. API Endpoints
Test the backend API directly:

```bash
# Health check
GET http://localhost:5000/api/auth/me

# AI Bill Analysis
POST http://localhost:5000/api/ai/analyze-bill
Content-Type: multipart/form-data
Body: billImage (file)

# AI Voice Processing
POST http://localhost:5000/api/ai/process-voice
Content-Type: application/json
Body: {"voiceText": "I spent 500 on milk yesterday"}
```

## 🔍 Troubleshooting

### MongoDB Connection Issues
```bash
# Error: MongoDB connection failed
# Solution 1: Start local MongoDB
mongod --dbpath "C:\data\db"

# Solution 2: Use MongoDB Atlas
# Update MONGODB_URI in server/.env with Atlas connection string
```

### Port Already in Use
```bash
# Error: Port 3000/5000 already in use
# Solution: Kill existing processes
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F
```

### Gemini API Issues
```bash
# Error: Gemini API key invalid
# Solution: Verify API key in server/.env
GEMINI_API_KEY=AIzaSyA7mzy9PXeMClojxqeuPGqU2vNAyXCj2dg
```

### Dependencies Issues
```bash
# Error: Module not found
# Solution: Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## 📱 Application Features

### ✅ Implemented Features
- **User Authentication** (Register/Login)
- **Personal Expense Tracking** with categories
- **Interactive Dashboard** with charts
- **AI Bill Scanner** using Gemini API
- **AI Voice Input** with natural language processing
- **Group Expense Splitting** with real-time updates
- **Smart Categorization** and auto-completion
- **Responsive Design** for mobile/desktop

### 🎨 UI Components
- Modern Tailwind CSS design
- Interactive Chart.js visualizations
- Real-time Socket.io updates
- Mobile-responsive layout
- Loading states and animations

### 🤖 AI Capabilities
- **Bill Analysis**: Extract amount, date, merchant, category
- **Voice Processing**: Natural language expense input
- **Smart Categorization**: Context-aware expense categories
- **Confidence Scoring**: AI reliability indicators

## 🌐 Production Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/splitify
JWT_SECRET=your_super_secure_jwt_secret
GEMINI_API_KEY=your_gemini_api_key
CLIENT_URL=https://your-frontend-domain.com
```

### Deployment Options
- **Frontend**: Vercel, Netlify, or AWS S3
- **Backend**: Heroku, Railway, or AWS EC2
- **Database**: MongoDB Atlas (recommended)

## 📞 Support

If you encounter any issues:
1. Check the console logs in browser (F12)
2. Check server logs in terminal
3. Verify all environment variables are set
4. Ensure MongoDB is running and accessible

## 🎉 Success Indicators

When everything is working correctly, you should see:
- ✅ Frontend loads at http://localhost:3000
- ✅ Backend API responds at http://localhost:5000
- ✅ MongoDB connection successful
- ✅ AI features work (bill scan & voice input)
- ✅ Charts and dashboard display data
- ✅ Real-time updates in groups

The application is now ready for full testing and demonstration!
